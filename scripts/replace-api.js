const fs = require('fs')
const path = require('path')

const filePath = path.resolve(
  __dirname,
  '../dist/lib/VFormDesigner.umd.js'
)

try {
  let content = fs.readFileSync(filePath, 'utf8')

  // 首先尝试匹配已经被错误处理的格式
  let regex =
    /const\s+VUE_APP_BASE_API\s*=\s*"process\.env\.VUE_APP_BASE_API";/g
  let replacement =
    'const VUE_APP_BASE_API = process.env.VUE_APP_BASE_API;'

  // console.log('Replacement string:', JSON.stringify(replacement))

  // 检查是否找到匹配项
  if (!regex.test(content)) {
    // 如果没有找到，尝试匹配原始的 /dev-api 格式
    regex = /const\s+VUE_APP_BASE_API\s*=\s*"\/dev-api";/g
    if (!regex.test(content)) {
      console.log(
        'No matching API configuration found in the file.'
      )
      process.exit(0)
    }
  }

  // 重新读取文件内容（因为test()会改变正则表达式的lastIndex）
  content = fs.readFileSync(filePath, 'utf8')

  // 执行替换
  const newContent = content.replace(regex, replacement)

  // 检查替换结果
  // console.log('Before replacement:', content.match(/const\s+VUE_APP_BASE_API\s*=\s*[^;]+;/)?.[0])
  // console.log('After replacement:', newContent.match(/const\s+VUE_APP_BASE_API\s*=\s*[^;]+;/)?.[0])

  // 写入文件
  fs.writeFileSync(filePath, newContent, 'utf8')
  console.log('API configuration replaced successfully!')
} catch (error) {
  console.error('Error replacing API configuration:', error)
  process.exit(1)
}
