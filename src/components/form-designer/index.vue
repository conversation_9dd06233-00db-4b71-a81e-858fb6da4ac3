<template>
  <el-container class="vform-container full-height">
    <el-container>
      <!-- 左侧组件列表 -->
      <el-aside class="side-panel">
        <widget-panel
          :designer="designer"
          :parent-container-name="parentContainerName"
        />
      </el-aside>
      <!-- 展示列表 -->
      <el-container class="center-layout-container">
        <el-header class="toolbar-header">
          <toolbar-panel
            :designer="designer"
            :global-dsv="globalDsv"
            ref="toolbarRef"
          >
            <template
              v-for="(idx, slotName) in $slots"
              #[slotName]
            >
              <slot :name="slotName"></slot>
            </template>
          </toolbar-panel>
        </el-header>
        <el-main class="form-widget-main">
          <el-scrollbar1 class="container-scroll-bar">
            <v-form-widget
              :designer="designer"
              :form-config="designer.formConfig"
              :global-dsv="globalDsv"
              ref="formRef"
            >
            </v-form-widget>
          </el-scrollbar1>
        </el-main>
      </el-container>
      <!-- 右侧配置栏 -->
      <div
        class="right-setting"
        style="width: 320px; height: 100%"
      >
        <setting-panel
          :designer="designer"
          :selected-widget="designer.selectedWidget"
          :selected-search-config="
            designer.selectedSearchConfig
          "
          :parent-container-name="parentContainerName"
          :form-config="designer.formConfig"
          :global-dsv="globalDsv"
        />
      </div>
    </el-container>
  </el-container>
</template>

<script>
import WidgetPanel from './widget-panel/index.vue'
import ToolbarPanel from './toolbar-panel/index.vue'
import SettingPanel from './setting-panel/index.vue'
import VFormWidget from './form-widget/index.vue'
import { createDesigner } from '@/components/form-designer/designer'
import {
  addWindowResizeHandler,
  deepClone,
  getAllContainerWidgets,
  getAllFieldWidgets,
  getQueryParam,
  traverseAllWidgets
} from '@/utils/util'
import i18n, { changeLocale } from '@/utils/i18n'
import SvgIcon from '@/components/svg-icon'
import scrollbar from '@/components/scrollbar/src/main.js'
export default {
  name: 'VFormDesigner',
  componentName: 'VFormDesigner',
  mixins: [i18n],
  components: {
    'el-scrollbar1': scrollbar,
    WidgetPanel,
    ToolbarPanel,
    SettingPanel,
    VFormWidget,
    SvgIcon
  },
  props: {
    handleType: {
      type: String,
      default: () => 'add'
    },
    parentContainerName: {
      type: String,
      default: () => ''
    },
    downUploadFileCallback: {
      type: Function,
      default: () => () => {}
    },
    /* 后端字段列表API */
    fieldListApi: {
      type: Object,
      default: null
    },

    /* 禁止显示的组件名称数组 */
    bannedWidgets: {
      type: Array,
      default: () => []
    },

    designerConfig: {
      type: Object,
      default: () => {
        return {
          languageMenu: true, //是否显示语言切换菜单
          externalLink: true, //是否显示GitHub、文档等外部链接
          formTemplates: true, //是否显示表单模板
          eventCollapse: true, //是否显示组件事件属性折叠面板
          widgetNameReadonly: false, //禁止修改组件名称

          clearDesignerButton: true, //是否显示清空设计器按钮
          previewFormButton: true, //是否显示预览表单按钮
          importJsonButton: true, //是否显示导入JSON按钮
          exportJsonButton: true, //是否显示导出JSON器按钮
          exportCodeButton: true, //是否显示导出代码按钮
          generateSFCButton: true, //是否显示生成SFC按钮
          toolbarMaxWidth: 420, //设计器工具按钮栏最大宽度（单位像素）
          toolbarMinWidth: 300, //设计器工具按钮栏最小宽度（单位像素）

          presetCssCode: '', //设计器预设CSS样式代码

          resetFormJson: false //是否在设计器初始化时将表单内容重置为空
        }
      }
    },

    /* 全局数据源变量 */
    globalDsv: {
      type: Object,
      default: () => ({})
    },
    previewState: {
      // 是否表单预览状态
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      curLangName: '',

      vsCodeFlag: false,

      caseName: '',

      scrollerHeight: 0,

      designer: createDesigner(this),

      fieldList: [],

      widgetRefList: {},

      settingRefList: {}
    }
  },
  provide() {
    return {
      settingRefList: this.settingRefList,
      refList: this.widgetRefList,
      serverFieldList: this.fieldList,
      handleType: this.handleType,
      downUploadFileCallback: this.downUploadFileCallback,
      getDesignerConfig: () => this.designerConfig,
      getBannedWidgets: () => this.bannedWidgets,
      previewState: this.previewState
    }
  },
  created() {
    this.vsCodeFlag = getQueryParam('vscode') == 1
    this.caseName = getQueryParam('case')
  },
  mounted() {
    this.initLocale()
    this.scrollerHeight =
      window.innerHeight - 56 - 36 + 'px'
    addWindowResizeHandler(() => {
      this.$nextTick(() => {
        this.scrollerHeight =
          window.innerHeight - 56 - 36 + 'px'
      })
    })
  },
  methods: {
    initLocale() {
      let curLocale = localStorage.getItem('v_form_locale')
      if (!!this.vsCodeFlag) {
        curLocale = curLocale || 'en-US'
      } else {
        curLocale = curLocale || 'zh-CN'
      }
      this.curLangName = this.i18nt(
        'application.' + curLocale
      )
      this.changeLanguage(curLocale)
    },

    handleLanguageChanged(command) {
      this.changeLanguage(command)
      this.curLangName = this.i18nt(
        'application.' + command
      )
    },

    changeLanguage(langName) {
      changeLocale(langName)
    },

    setFormJson(formJson) {
      let modifiedFlag = false
      if (!!formJson) {
        if (typeof formJson === 'string') {
          modifiedFlag = this.designer.loadFormJson(
            JSON.parse(formJson)
          )
        } else if (formJson.constructor === Object) {
          modifiedFlag =
            this.designer.loadFormJson(formJson)
        }

        if (modifiedFlag) {
          this.designer.emitHistoryChange()
        }
      }
    },
    /**
     * 保存为json
     */
    getFormJson() {
      return {
        widgetList: deepClone(this.designer.widgetList),
        formConfig: deepClone(this.designer.formConfig),
        searchConfigList: deepClone(
          this.designer.searchConfigList
        )
      }
    },

    clearDesigner() {
      this.$refs.toolbarRef.clearFormWidget()
    },

    /**
     * 刷新表单设计器
     */
    refreshDesigner() {
      //this.designer.loadFormJson( this.getFormJson() )  //只有第一次调用生效？？

      const fJson = this.getFormJson()
      this.designer.clearDesigner(true) // 不触发历史记录变更
      this.designer.loadFormJson(fJson)
    },

    /**
     * 预览表单
     */
    previewForm() {
      this.$refs.toolbarRef.previewForm()
    },

    /**
     * 导入表单JSON
     */
    importJson() {
      this.$refs.toolbarRef.importJson()
    },

    /**
     * 导出表单JSON
     */
    exportJson() {
      this.$refs.toolbarRef.exportJson()
    },

    /**
     * 导出Vue/HTML代码
     */
    exportCode() {
      this.$refs.toolbarRef.exportCode()
    },

    /**
     * 生成SFC代码
     */
    generateSFC() {
      this.$refs.toolbarRef.generateSFC()
    },

    /**
     * 获取所有字段组件
     * @returns {*[]}
     */
    getFieldWidgets(widgetList = null) {
      return !!widgetList
        ? getAllFieldWidgets(widgetList)
        : getAllFieldWidgets(this.designer.widgetList)
    },

    /**
     * 获取所有容器组件
     * @returns {*[]}
     */
    getContainerWidgets(widgetList = null) {
      return !!widgetList
        ? getAllContainerWidgets(widgetList)
        : getAllContainerWidgets(this.designer.widgetList)
    },

    /**
     * 升级表单json，以补充最新的组件属性
     * @param formJson
     */
    upgradeFormJson(formJson) {
      if (!formJson.widgetList || !formJson.formConfig) {
        this.$message.error('Invalid form json!')
        return
      }

      traverseAllWidgets(formJson.widgetList, (w) => {
        this.designer.upgradeWidgetConfig(w)
      })
      this.designer.upgradeFormConfig(formJson.formConfig)

      return formJson
    },

    getWidgetRef(widgetName, showError = false) {
      return this.$refs['formRef'].getWidgetRef(
        widgetName,
        showError
      )
    },

    getSelectedWidgetRef() {
      return this.$refs['formRef'].getSelectedWidgetRef()
    }

    //TODO: 增加更多方法！！
  }
}
</script>

<style lang="scss" scoped>
.el-scrollbar__wrap::-webkit-scrollbar {
  width: 20px;
  height: 20px;
}

.el-container.vform-container {
  background: #fff;

  ::v-deep aside {
    /* 防止aside样式被外部样式覆盖！！ */
    margin: 0;
    padding: 0;
    background: inherit;
  }
}

.el-container.full-height {
  height: 100%;
  overflow-y: hidden;
}

.el-container.center-layout-container {
  min-width: 680px;
  border-left: 2px dotted #ebeef5;
  border-right: 2px dotted #ebeef5;
}

.el-header.main-header {
  border-bottom: 2px dotted #ebeef5;
  height: 48px !important;
  line-height: 48px !important;
  min-width: 800px;
}

div.main-title {
  font-size: 18px;
  color: #242424;
  display: flex;
  align-items: center;
  justify-items: center;

  img {
    cursor: pointer;
    width: 36px;
    height: 36px;
  }

  span.bold {
    font-size: 20px;
    font-weight: bold;
    margin: 0 6px 0 6px;
  }

  span.version-span {
    font-size: 14px;
    color: #101f1c;
    margin-left: 6px;
  }
}

.float-left {
  float: left;
}

.float-right {
  float: right;
}

.el-dropdown-link {
  margin-right: 12px;
  cursor: pointer;
}

div.external-link a {
  font-size: 13px;
  text-decoration: none;
  margin-right: 10px;
  color: #606266;
}

.el-header.toolbar-header {
  font-size: 14px;
  border-bottom: 1px dotted #cccccc;
  height: 42px !important;
  //line-height: 42px !important;
}

.el-aside.side-panel {
  width: 265px !important;
  overflow-y: hidden;
}

.el-main.form-widget-main {
  padding: 0;
  height: 100%;
  position: relative;
  // overflow-x: hidden;
  overflow: hidden;
}

.container-scroll-bar {
  height: 100%;
}

::v-deep .el-scrollbar__wrap1 {
  overflow-y: auto;
  height: 100%;
}

::v-deep .el-scrollbar__wrap1::-webkit-scrollbar {
  width: 15px !important;
}
</style>
