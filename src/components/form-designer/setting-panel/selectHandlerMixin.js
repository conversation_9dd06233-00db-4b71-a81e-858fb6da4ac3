export default {
  inject: ['refList'],
  props: {
    selectedWidget: Object,
    optionModel: Object
  },
  data() {
    return {
      responseKVList: []
    }
  },
  methods: {
    initResponseKVList(responseKVList) {
      this.responseKVList = responseKVList
    },

    handleInitSelectOptionItems(value) {
      if (!value) return
      const name = this.optionModel.name
      if (!name) return
      const selectedWidgetRef = this.refList[name]
      if (selectedWidgetRef) {
        selectedWidgetRef.initSelectOptionItems(false)
      } else {
        console.error('组件实例未找到')
      }
    },
    /**
     * 获取接口参数下拉项
     * @param {string} hanldType
     * @returns {Promise<void>}
     */
    async handleGetKVList(hanldType) {
      if (hanldType === 'interfaceAddress') {
        const requestUrl =
          this.optionModel.interfaceAddressRequestUrl

        if (!requestUrl) {
          return
        }

        const method =
          this.optionModel.interfaceAddressRequestMethod

        let interfaceAddressResult = null
        if (method.toLowerCase() == 'get') {
          interfaceAddressResult = await fetch(requestUrl, {
            method: 'get',
            headers: {
              Authorization: `Bearer ${this.authorization}`
            }
          }).then((res) => res.json())
        } else {
          interfaceAddressResult = await fetch(requestUrl, {
            method: 'post',
            headers: {
              Authorization: `Bearer ${this.authorization}`,
              'Content-Type': 'application/json'
            }
          }).then((res) => res.json())
        }
        if (interfaceAddressResult.code == 200) {
          const options = interfaceAddressResult.data
          if (!options || !Array.isArray(options)) {
            this.$message.error(
              this.i18nt('designer.hint.dataNotAllow')
            )
            return
          }
          const settingRefList = this.settingRefList
          const interfaceAddressResponseLabelKeyEditor =
            settingRefList[
              'interfaceAddressResponseLabelKey-editor'
            ]
          console.log(
            'interfaceAddressResponseLabelKeyEditor--interfaceAddressResponseLabelKeyEditor',
            interfaceAddressResponseLabelKeyEditor
          )

          const interfaceAddressResponseValueKeyEditor =
            settingRefList[
              'interfaceAddressResponseValueKey-editor'
            ]

          console.log(
            'interfaceAddressResponseValueKeyEditor--interfaceAddressResponseValueKeyEditor',
            interfaceAddressResponseValueKeyEditor
          )

          const firstOption = options[0]
          if (!firstOption) {
            this.$message.error(
              this.i18nt('designer.hint.dataNotAllow')
            )
            if (interfaceAddressResponseLabelKeyEditor) {
              interfaceAddressResponseLabelKeyEditor.initResponseKVList(
                []
              )
            } else {
              console.log(
                'interfaceAddressResponseLabelKeyEditor 不存在'
              )
            }
            if (interfaceAddressResponseValueKeyEditor) {
              interfaceAddressResponseValueKeyEditor.initResponseKVList(
                []
              )
            }
          } else {
            const interfaceAddressResponseKVList =
              Object.keys(firstOption).map((key) => {
                return {
                  label: key,
                  value: key
                }
              })
            if (interfaceAddressResponseLabelKeyEditor) {
              interfaceAddressResponseLabelKeyEditor.initResponseKVList(
                interfaceAddressResponseKVList
              )
            }
            if (interfaceAddressResponseValueKeyEditor) {
              interfaceAddressResponseValueKeyEditor.initResponseKVList(
                interfaceAddressResponseKVList
              )
            }
          }
        }
      }

      if (hanldType === 'dataMarkt') {
        const dataMarketRequestId =
          this.optionModel.dataMarketRequestId
        if (!dataMarketRequestId) {
          return
        }
        const VUE_APP_BASE_API =
          process.env.VUE_APP_BASE_API
        // const VUE_APP_BASE_API = process.env.VUE_APP_BASE_API;
        const getRequestDetailUrl = `${VUE_APP_BASE_API}/data/market/dataApis/detail/${dataMarketRequestId}`
        const { apiHeader, apiUrl, reqMethod, apiVersion } =
          await fetch(getRequestDetailUrl, {
            method: 'get',
            headers: {
              Authorization: `Bearer ${this.authorization}`
            }
          })
            .then((res) => res.json())
            .then((result) => {
              const requestDetail = {
                apiVersion: '',
                apiHeader: {},
                apiUrl: '',
                reqMethod: ''
              }
              if (result.code == 200) {
                requestDetail.apiHeader = result.data.header
                requestDetail.apiVersion =
                  result.data.data.apiVersion
                requestDetail.apiUrl =
                  result.data.data.apiUrl
                requestDetail.reqMethod =
                  result.data.data.reqMethod
              }
              return requestDetail
            })
        const baseRequestUrl = `${VUE_APP_BASE_API}/data/api/services/${apiVersion}${apiUrl}`
        let dataMarketResult = null
        if (reqMethod.toLocaleLowerCase() == 'get') {
          const requestUrl = `${baseRequestUrl}`
          dataMarketResult = await fetch(requestUrl, {
            method: 'get',
            headers: {
              ...apiHeader,
              Authorization: `Bearer ${this.authorization}`
            }
          }).then((res) => res.json())
        } else {
          dataMarketResult = await fetch(baseRequestUrl, {
            method: 'post',
            headers: {
              ...apiHeader,
              'Content-Type': 'application/json',
              Authorization: `Bearer ${this.authorization}`
            }
          }).then((res) => res.json())
        }
        if (dataMarketResult.code == 200) {
          const options = dataMarketResult.data
          if (!options || !Array.isArray(options)) {
            this.$message.error(
              this.i18nt('designer.hint.dataNotAllow')
            )
            return
          }
          const firstOption = options[0]
          const settingRefList = this.settingRefList

          console.log(
            'settingRefList-settingRefList',
            settingRefList
          )

          const responseLabelKeyEditor =
            settingRefList[
              'dataMarketResponseLabelKey-editor'
            ]
          console.log(
            'responseLabelKeyEditor-responseLabelKeyEditor',
            responseLabelKeyEditor
          )

          const responseValueKeyEditor =
            settingRefList[
              'dataMarketResponseValueKey-editor'
            ]
          console.log(
            'responseValueKeyEditor--responseValueKeyEditor',
            responseValueKeyEditor
          )

          if (!firstOption) {
            this.$message.error(
              this.i18nt('designer.hint.dataNotAllow')
            )
            if (responseLabelKeyEditor) {
              responseLabelKeyEditor.initResponseKVList([])
            }
            if (responseValueKeyEditor) {
              responseValueKeyEditor.initResponseKVList([])
            }
          } else {
            const responseKVList = Object.keys(
              firstOption
            ).map((key) => {
              return {
                label: key,
                value: key
              }
            })
            if (responseLabelKeyEditor) {
              responseLabelKeyEditor.initResponseKVList(
                responseKVList
              )
            }
            if (responseValueKeyEditor) {
              responseValueKeyEditor.initResponseKVList(
                responseKVList
              )
            }
          }
        } else {
          this.$message.error(
            this.i18nt('designer.hint.dataNotAllow')
          )
        }
      }
    }
  }
}
