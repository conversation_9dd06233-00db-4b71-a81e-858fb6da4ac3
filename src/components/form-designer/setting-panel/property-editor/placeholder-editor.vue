<template>
  <el-form-item
    :label="i18nt('designer.setting.placeholder')"
  >
    <el-input
      type="text"
      v-model="optionModel.placeholder"
    ></el-input>
  </el-form-item>
</template>

<script>
import i18n from '@/utils/i18n'
import propertyMixin from '@/components/form-designer/setting-panel/property-editor/propertyMixin'
import settingRefMixinDesign from '../settingRefMixinDesign'
export default {
  name: 'placeholder-editor',
  mixins: [settingRefMixinDesign, i18n, propertyMixin],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object
  }
}
</script>

<style scoped></style>
