<template>
  <el-form-item class="match-type-editor" label="匹配方式">
    <el-select
      v-model="optionModel.matchType"
      placeholder="匹配方式"
      clearable
    >
      <el-option
        v-if="hasAccurateQuery()"
        label="精准查询"
        :value="2"
      ></el-option>
      <el-option
        v-if="hasFuzzyQuery()"
        label="模糊查询"
        :value="3"
      ></el-option>
      <el-option
        v-if="hasTimeRange()"
        label="时间范围"
        :value="4"
      ></el-option>
    </el-select>
    <el-button
      v-if="searchConfigFlag"
      type="text"
      @click="handleCopySearchConfig"
    >
      从组件配置复制
    </el-button>
  </el-form-item>
</template>

<script>
import settingRefMixinDesign from '../settingRefMixinDesign'
export default {
  name: 'match-type-editor',
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
    searchConfigFlag: {
      type: Boolean,
      default: false
    }
  },
  mixins: [settingRefMixinDesign],
  data() {
    return {}
  },
  methods: {
    handleCopySearchConfig() {
      this.designer.handleCopySearchConfig()
    },
    /**
     * 精准查询
     */
    hasAccurateQuery() {
      const accurateQueryTypes = [
        'input',
        'login-person',
        'textarea',
        'number',
        'select',
        'radio',
        'checkbox',
        // 'time',
        // 'date'，
        'location'
      ]
      return accurateQueryTypes.includes(
        this.selectedWidget.type
      )
    },
    /**
     * 模糊查询
     */
    hasFuzzyQuery() {
      const fuzzyQueryTypes = [
        'input',
        'login-person',
        'textarea',
        'location'
      ]
      return fuzzyQueryTypes.includes(
        this.selectedWidget.type
      )
    },
    /**
     * 时间范围
     */
    hasTimeRange() {
      const timeRangeTypes = ['date', 'time']
      return timeRangeTypes.includes(
        this.selectedWidget.type
      )
    }
  }
}
</script>
<style>
.match-type-editor {
  position: relative;
}
</style>
