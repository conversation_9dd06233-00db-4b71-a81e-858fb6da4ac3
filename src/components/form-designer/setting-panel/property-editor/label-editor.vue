<template>
  <el-form-item
    :label="i18nt('designer.setting.label')"
    v-if="!noLabelSetting"
  >
    <el-input
      :disabled="labelDisabled"
      type="text"
      v-model="optionModel.label"
      @change="handleLabelChange"
    ></el-input>
  </el-form-item>
</template>

<script>
import i18n from '@/utils/i18n'
import settingRefMixinDesign from '../settingRefMixinDesign'
export default {
  name: 'label-editor',
  mixins: [settingRefMixinDesign, i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object
  },
  computed: {
    labelDisabled() {
      return (
        this.selectedWidget.options.matchType !== undefined
      )
    },
    noLabelSetting() {
      return (
        this.selectedWidget.type === 'static-text' ||
        this.selectedWidget.type === 'html-text'
      )
    }
  },
  methods: {
    handleLabelChange(newLabel) {
      this.designer.updateSearchConfigLabel(newLabel)
    }
  }
}
</script>

<style lang="scss" scoped></style>
