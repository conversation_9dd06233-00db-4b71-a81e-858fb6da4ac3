<template>
  <el-form-item
    :label="i18nt('designer.setting.defaultValue')"
  >
    <el-tabs
      v-model="optionModel.defaultValueSettingtTabName"
    >
      <el-tab-pane
        :label="i18nt('designer.setting.fixedValue')"
        name="fixedValue"
      >
        <!-- 原来的 -->
        <el-date-picker
          :type="optionModel.type"
          v-model="optionModel.defaultValue"
          @change="emitDefaultValueChange"
          :format="optionModel.format"
          :value-format="optionModel.valueFormat"
          style="width: 100%"
        >
        </el-date-picker>
      </el-tab-pane>
      <!-- 动态值 -->
      <el-tab-pane
        :label="i18nt('designer.setting.dynamicValue')"
        name="dynamicValue"
      >
        <el-input
          v-model="optionModel.dynamicValue"
          @change="emitDefaultValueChange"
          :placeholder="
            i18nt(
              'designer.setting.dynamicValuePlaceholder'
            )
          "
        >
        </el-input>
      </el-tab-pane>
    </el-tabs>
  </el-form-item>
</template>

<script>
import i18n from '@/utils/i18n'
import propertyMixin from '@/components/form-designer/setting-panel/property-editor/propertyMixin'

export default {
  name: 'date-defaultValue-editor',
  mixins: [i18n, propertyMixin],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-tabs__header {
  margin-bottom: 5px;

  .el-tabs__item {
    height: 28px;
    line-height: 28px;
  }
}
</style>
