<template>
  <el-form-item label-width="0">
    <el-checkbox
      v-model="optionModel.latestAvailableTimeSwitch"
      >最晚可选时间</el-checkbox
    >
    <div class="latestAvailableTime-container">
      <el-select
        v-model="optionModel.latestAvailableTimeType"
        placeholder="请选择"
      >
        <el-option label="动态值" value="dynamicValue">
        </el-option>
      </el-select>
      <el-select
        v-model="optionModel.latestAvailableTime"
        placeholder="请选择"
      >
        <el-option
          v-for="item in dynamicValueOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
        </el-option>
      </el-select>
    </div>
  </el-form-item>
</template>

<script>
import i18n from '@/utils/i18n'

export default {
  name: 'latestAvailableTime-editor',
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object
  },
  data() {
    return {
      dynamicValueOptions: [
        {
          label: '当天',
          value: 0
        },
        {
          label: '昨天',
          value: -1
        },
        {
          label: '明天',
          value: 1
        },
        {
          label: '7天前',
          value: -7
        },
        {
          label: '7天后',
          value: 7
        },
        {
          label: '30天前',
          value: -30
        },
        {
          label: '30天后',
          value: 30
        }
      ]
    }
  }
}
</script>

<style scoped>
.latestAvailableTime-container {
  display: flex;
}
</style>
