<template>
  <el-form-item label-width="0">
    <el-form-item label="计算模式">
      <el-select v-model="optionModel.computedSettingModel">
        <el-option
          v-for="item in computedSettingModelOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
        </el-option>
      </el-select>
    </el-form-item>
    <el-form-item
      label-width="0"
      v-if="hasComputedSetting(optionModel)"
    >
      <el-input
        type="textarea"
        v-model="optionModel.computedSetting"
        :placeholder="
          i18nt(
            'designer.setting.computedSettingPlaceholder'
          )
        "
      ></el-input>
    </el-form-item>
    <el-form-item
      label=" "
      v-if="hasComputedSettingCode(optionModel)"
    >
      <el-button
        @click="handleOpenComputedSettingCodeDialog"
        >编辑代码</el-button
      >
      <!-- 代码编辑器 -->
      <el-dialog
        :title="
          i18nt('designer.setting.editWidgetEventHandler')
        "
        :visible.sync="openComputedSettingCodeDialogFlag"
        v-if="openComputedSettingCodeDialogFlag"
        :show-close="true"
        class="small-padding-dialog"
        append-to-body
        v-dialog-drag
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :destroy-on-close="true"
      >
        <el-alert
          type="info"
          :closable="false"
          :title="eventHeader"
        ></el-alert>
        <code-editor
          :check-return="true"
          :mode="'javascript'"
          :readonly="false"
          v-model="computedSettingCode"
          ref="ecEditor"
        ></code-editor>
        <el-alert
          type="info"
          :closable="false"
          title="}"
        ></el-alert>
        <div slot="footer" class="dialog-footer">
          <el-button
            @click="
              openComputedSettingCodeDialogFlag = false
            "
          >
            {{ i18nt('designer.hint.cancel') }}</el-button
          >
          <el-button
            type="primary"
            @click="saveEventHandler"
          >
            {{ i18nt('designer.hint.confirm') }}</el-button
          >
        </div>
      </el-dialog>
    </el-form-item>
  </el-form-item>
</template>

<script>
import i18n from '@/utils/i18n'
import propertyMixin from '@/components/form-designer/setting-panel/property-editor/propertyMixin'
import CodeEditor from '@/components/code-editor/index'
export default {
  name: 'computedSetting-editor',
  mixins: [i18n, propertyMixin],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object
  },
  components: {
    CodeEditor
  },
  data() {
    return {
      computedSettingModelOptions: [
        {
          label: '默认模式',
          value: 'default'
        },
        {
          label: '简易模式',
          value: 'simple'
        },
        {
          label: '代码模式',
          value: 'code'
        }
      ],
      openComputedSettingCodeDialogFlag: false,
      eventHeader: ''
    }
  },
  computed: {
    hasComputedSetting() {
      return (optionModel) => {
        return optionModel.computedSettingModel === 'simple'
      }
    },
    hasComputedSettingCode() {
      return (optionModel) => {
        return optionModel.computedSettingModel === 'code'
      }
    },
    computedSettingModel: {
      get() {
        // if (this.optionModel.computedSettingModel === undefined) {
        //   this.optionModel.computedSettingModel = 'default'
        // }
        return (
          this.optionModel.computedSettingModel || 'default'
        )
      },
      set(value) {
        this.optionModel.computedSettingModel = value
      }
    },
    computedSettingCode: {
      get() {
        return this.optionModel.computedSettingCode || ''
      },
      set(value) {
        this.optionModel.computedSettingCode = value
      }
    }
  },
  methods: {
    handleOpenComputedSettingCodeDialog() {
      this.eventHeader = `${this.optionModel.name} changeby {`
      this.openComputedSettingCodeDialogFlag = true
    },
    saveEventHandler() {
      const hasReturn =
        this.$refs['ecEditor'].hasValidReturnStatement()
      if (!hasReturn) {
        this.$message.error('缺少有效return语句')
        return
      }
      const codeHints =
        this.$refs.ecEditor.getEditorAnnotations()
      let syntaxErrorFlag = false
      if (!!codeHints && codeHints.length > 0) {
        codeHints.forEach((chItem) => {
          if (chItem.type === 'error') {
            syntaxErrorFlag = true
          }
        })

        if (syntaxErrorFlag) {
          this.$message.error(
            this.i18nt(
              'designer.setting.syntaxCheckWarning'
            )
          )
          return
        }
      }

      // this.selectedWidget.options[this.curEventName] =
      //   this.eventHandlerCode
      this.openComputedSettingCodeDialogFlag = false
    }
  }
}
</script>

<style lang="scss" scoped>
.small-padding-dialog {
  ::v-deep .el-dialog__body {
    padding: 6px 15px 12px 15px;
  }
}
</style>
