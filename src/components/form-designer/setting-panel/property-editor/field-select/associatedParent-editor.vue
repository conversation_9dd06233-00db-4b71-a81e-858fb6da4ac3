<template>
  <el-form-item
    :label="i18nt('designer.setting.associatedParent')"
  >
    <el-select
      clearable
      @change="handleAssociatedParentChange"
      v-model="associatedParent"
      :placeholder="
        i18nt(
          'designer.setting.associatedParentPlaceholder'
        )
      "
      multiple
    >
      <el-option
        v-for="item in associatedParentOptions"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      >
      </el-option>
    </el-select>
  </el-form-item>
</template>

<script>
import i18n from '@/utils/i18n'
import propertyMixin from '@/components/form-designer/setting-panel/property-editor/propertyMixin'

export default {
  name: 'associatedParent-editor',
  mixins: [i18n, propertyMixin],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object
  },
  data() {
    return {
      associatedParentOptions: []
    }
  },
  computed: {
    associatedParent: {
      get() {
        const associatedParent =
          this.optionModel.associatedParent
        // 兼容之前的配置方式
        return Array.isArray(associatedParent)
          ? associatedParent
          : associatedParent
          ? [associatedParent]
          : []
      },
      set(value) {
        this.optionModel.associatedParent = value
      }
    }
  },
  watch: {
    'designer.widgetList': {
      handler() {
        this.getAssociatedParentOptions()
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    /**
     * 获取关联父级选项
     */
    getAssociatedParentOptions() {
      this.associatedParentOptions = []

      const findAssociatedParentOptions = (widgetList) => {
        if (!widgetList) return
        widgetList.forEach((item) => {
          if (item.cols && item.cols.length > 0) {
            findAssociatedParentOptions(item.cols)
          } else if (
            item.widgetList &&
            item.widgetList.length > 0
          ) {
            findAssociatedParentOptions(item.widgetList)
          } else {
            const isSelect = item.type == 'select' // 必须是下拉框
            const isNotSelf =
              item.id !== this.selectedWidget.id // 不是自己
            if (isSelect && isNotSelf) {
              this.associatedParentOptions.push({
                label: item.options.label,
                value: item.options.name
              })
            }
          }
        })
      }
      findAssociatedParentOptions(this.designer.widgetList)
    },
    handleAssociatedParentChange() {}
  }
}
</script>

<style scoped></style>
