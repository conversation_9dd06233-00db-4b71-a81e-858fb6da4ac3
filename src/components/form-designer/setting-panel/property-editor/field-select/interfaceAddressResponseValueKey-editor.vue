<template>
  <el-form-item label="value 别名" v-if="hasConfig">
    <el-select
      filterable
      v-model="interfaceAddressResponseValueKey"
      @change="handleInitSelectOptionItems"
      clearable
      :placeholder="
        i18nt(
          'designer.setting.interfaceAddressResponseValueKeyPlaceholder'
        )
      "
      style="width: 100%"
    >
      <el-option
        v-for="(item, index) in responseKVList"
        :label="item.label"
        :value="item.value"
        :key="index"
      >
      </el-option>
    </el-select>
  </el-form-item>
</template>

<script>
import i18n from '@/utils/i18n'
import settingRefMixinDesign from '../../settingRefMixinDesign'
import selectHandlerMixin from '../../selectHandlerMixin'
export default {
  inject: ['refList'],
  name: 'interfaceAddressResponseValueKey-editor',
  mixins: [settingRefMixinDesign, selectHandlerMixin, i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object
  },
  computed: {
    interfaceAddressResponseValueKey: {
      get() {
        // if (
        //   this.optionModel.optionSettingTabType === 'enter'
        // ) {
        //   return this.optionModel
        //     .interfaceAddressResponseValueKey
        // } else if (
        //   this.optionModel.optionSettingTabType === 'search'
        // ) {
        //   return this.optionModel
        //     .searchInterfaceAddressResponseValueKey
        // } else {
        //   return this.optionModel
        //     .interfaceAddressResponseValueKey
        // }
        return this.optionModel
          .interfaceAddressResponseValueKey
      },
      set(value) {
        // if (
        //   this.optionModel.optionSettingTabType === 'enter'
        // ) {
        //   this.optionModel.interfaceAddressResponseValueKey =
        //     value
        // } else if (
        //   this.optionModel.optionSettingTabType === 'search'
        // ) {
        //   this.optionModel.searchInterfaceAddressResponseValueKey =
        //     value
        // } else {
        //   this.optionModel.interfaceAddressResponseValueKey =
        //     value
        // }
        this.optionModel.interfaceAddressResponseValueKey =
          value
      }
    },
    hasConfig() {
      return (
        this.optionModel.optionSettingType ===
        'interfaceAddress'
      )
      // if (
      //   this.optionModel.optionSettingTabType === 'enter'
      // ) {
      //   return (
      //     this.optionModel.optionSettingType ===
      //     'interfaceAddress'
      //   )
      // } else if (
      //   this.optionModel.optionSettingTabType === 'search'
      // ) {
      //   return (
      //     this.optionModel.searchOptionSettingType ===
      //     'interfaceAddress'
      //   )
      // } else {
      //   return false
      // }
    }
  }
}
</script>

<style scoped></style>
