<template>
  <el-form-item label="数据市场" v-if="hasConfig">
    <el-select
      v-model="dataMarketRequestId"
      clearable
      filterable
      :placeholder="
        i18nt(
          'designer.setting.dataMarketRequestUrlPlaceholder'
        )
      "
      style="width: 100%"
    >
      <el-option
        v-for="item in this.dataMarketOptions"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      >
      </el-option>
    </el-select>
    <div style="text-align: right; margin-top: 5px">
      <!-- <el-button
        type="text"
        @click="handlevCopyEnterValue2Search('dataMarkt')"
        v-if="hasCopyButton"
      >
        复制
      </el-button> -->
      <el-button
        type="text"
        @click="handleGetKVList('dataMarkt')"
      >
        获取数据
      </el-button>
    </div>
  </el-form-item>
</template>

<script>
import i18n from '@/utils/i18n'
import settingRefMixinDesign from '../../settingRefMixinDesign'
import selectHandlerMixin from '../../selectHandlerMixin'
export default {
  inject: ['refList'],
  name: 'dataMarketRequestId-editor',
  mixins: [settingRefMixinDesign, selectHandlerMixin, i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object
  },
  data() {
    return {
      dataMarketOptions: []
    }
  },
  computed: {
    authorization() {
      return localStorage.getItem('BearToken')
    },

    dataMarketRequestId: {
      get() {
        // if (
        //   this.optionModel.optionSettingTabType === 'enter'
        // ) {
        //   return this.optionModel.dataMarketRequestId
        // } else if (
        //   this.optionModel.optionSettingTabType === 'search'
        // ) {
        //   return this.optionModel.searchDataMarketRequestId
        // } else {
        //   return this.optionModel.dataMarketRequestId
        // }
        return this.optionModel.dataMarketRequestId
      },
      set(value) {
        // if (
        //   this.optionModel.optionSettingTabType === 'enter'
        // ) {
        //   this.optionModel.dataMarketRequestId = value
        // } else if (
        //   this.optionModel.optionSettingTabType === 'search'
        // ) {
        //   this.optionModel.searchDataMarketRequestId = value
        // } else {
        //   this.optionModel.dataMarketRequestId = value
        // }
        this.optionModel.dataMarketRequestId = value
      }
    },
    hasConfig() {
      return (
        this.optionModel.optionSettingType === 'dataMarket'
      )

      // if (
      //   this.optionModel.optionSettingTabType === 'enter'
      // ) {
      //   return (
      //     this.optionModel.optionSettingType ===
      //     'dataMarket'
      //   )
      // } else if (
      //   this.optionModel.optionSettingTabType === 'search'
      // ) {
      //   return (
      //     this.optionModel.searchOptionSettingType ===
      //     'dataMarket'
      //   )
      // } else {
      //   return false
      // }
    }
  },
  mounted() {
    this.getDataMarketOptions()
  },
  methods: {
    /**
     * 获取 数据市场 下拉项
     */
    async getDataMarketOptions() {
      const VUE_APP_BASE_API = process.env.VUE_APP_BASE_API
      // const VUE_APP_BASE_API = process.env.VUE_APP_BASE_API;
      const requestUrl = `${VUE_APP_BASE_API}/data/market/dataApis/page?pageNum=1&pageSize=1000`
      const result = await fetch(requestUrl, {
        method: 'get',
        headers: {
          Authorization: `Bearer ${this.authorization}`
        }
      }).then((res) => res.json())
      if (result.code == 200) {
        this.dataMarketOptions = result.rows.map(
          (item) => ({
            label: item.apiName,
            value: item.id,
            ...item
          })
        )
      }
    }
  }
}
</script>
<style scoped></style>
