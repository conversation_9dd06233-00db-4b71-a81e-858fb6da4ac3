<template>
  <el-form-item label="请求参数" v-if="hasConfig">
    <el-input
      type="textarea"
      :rows="10"
      v-model="interfaceAddressRequestParams"
      :placeholder="
        i18nt(
          'designer.setting.interfaceAddressRequestParamsPlaceholder'
        )
      "
    ></el-input>
  </el-form-item>
</template>

<script>
import i18n from '@/utils/i18n'
import settingRefMixinDesign from '../../settingRefMixinDesign'
export default {
  inject: ['refList'],
  name: 'interfaceAddressRequestParams-editor',
  mixins: [settingRefMixinDesign, i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object
  },
  computed: {
    interfaceAddressRequestParams: {
      get() {
        // if (
        //   this.optionModel.optionSettingTabType === 'enter'
        // ) {
        //   return this.optionModel
        //     .interfaceAddressRequestParams
        // } else if (
        //   this.optionModel.optionSettingTabType === 'search'
        // ) {
        //   return this.optionModel
        //     .searchInterfaceAddressRequestParams
        // } else {
        //   return this.optionModel
        //     .interfaceAddressRequestParams
        // }
        return this.optionModel
          .interfaceAddressRequestParams
      },
      set(value) {
        // if (
        //   this.optionModel.optionSettingTabType === 'enter'
        // ) {
        //   this.optionModel.interfaceAddressRequestParams =
        //     value
        // } else if (
        //   this.optionModel.optionSettingTabType === 'search'
        // ) {
        //   this.optionModel.searchInterfaceAddressRequestParams =
        //     value
        // } else {
        //   this.optionModel.interfaceAddressRequestParams =
        //     value
        // }
        this.optionModel.interfaceAddressRequestParams =
          value
      }
    },
    hasConfig() {
      return (
        this.optionModel.optionSettingType ===
        'interfaceAddress'
      )
      // if (
      //   this.optionModel.optionSettingTabType === 'enter'
      // ) {
      //   return (
      //     this.optionModel.optionSettingType ===
      //     'interfaceAddress'
      //   )
      // } else if (
      //   this.optionModel.optionSettingTabType === 'search'
      // ) {
      //   return (
      //     this.optionModel.searchOptionSettingType ===
      //     'interfaceAddress'
      //   )
      // } else {
      //   return false
      // }
    }
  }
}
</script>

<style scoped></style>
