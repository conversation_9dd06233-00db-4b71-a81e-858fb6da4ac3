<template>
  <el-form-item label="请求地址" v-if="hasConfig">
    <el-input
      type="textarea"
      :autosize="{ minRows: 10 }"
      v-model="interfaceAddressRequestUrl"
      :placeholder="
        i18nt(
          'designer.setting.interfaceAddressRequestUrlPlaceholder'
        )
      "
    ></el-input>
  </el-form-item>
</template>

<script>
import i18n from '@/utils/i18n'
import settingRefMixinDesign from '../../settingRefMixinDesign'
export default {
  inject: ['refList'],
  name: 'interfaceAddressRequestUrl-editor',
  mixins: [settingRefMixinDesign, i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object
  },
  computed: {
    interfaceAddressRequestUrl: {
      get() {
        // if (
        //   this.optionModel.optionSettingTabType === 'enter'
        // ) {
        //   return this.optionModel.interfaceAddressRequestUrl
        // } else if (
        //   this.optionModel.optionSettingTabType === 'search'
        // ) {
        //   return this.optionModel
        //     .searchInterfaceAddressRequestUrl
        // } else {
        //   return this.optionModel.interfaceAddressRequestUrl
        // }
        return this.optionModel.interfaceAddressRequestUrl
      },
      set(value) {
        // if (
        //   this.optionModel.optionSettingTabType === 'enter'
        // ) {
        //   this.optionModel.interfaceAddressRequestUrl =
        //     value
        // } else if (
        //   this.optionModel.optionSettingTabType === 'search'
        // ) {
        //   this.optionModel.searchInterfaceAddressRequestUrl =
        //     value
        // } else {
        //   this.optionModel.interfaceAddressRequestUrl =
        //     value
        // }
        this.optionModel.interfaceAddressRequestUrl = value
      }
    },
    hasConfig() {
      return (
        this.optionModel.optionSettingType ===
        'interfaceAddress'
      )
      // if (
      //   this.optionModel.optionSettingTabType === 'enter'
      // ) {
      //   return (
      //     this.optionModel.optionSettingType ===
      //     'interfaceAddress'
      //   )
      // } else if (
      //   this.optionModel.optionSettingTabType === 'search'
      // ) {
      //   return (
      //     this.optionModel.searchOptionSettingType ===
      //     'interfaceAddress'
      //   )
      // } else {
      //   return false
      // }
    }
  }
}
</script>

<style scoped></style>
