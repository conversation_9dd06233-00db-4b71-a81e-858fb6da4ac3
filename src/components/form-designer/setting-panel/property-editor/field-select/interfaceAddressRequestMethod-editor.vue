<template>
  <el-form-item label="请求方法" v-if="hasConfig">
    <el-select
      v-model="interfaceAddressRequestMethod"
      :placeholder="
        i18nt(
          'designer.setting.interfaceAddressRequestMethodPlaceholder'
        )
      "
      style="width: 100%"
    >
      <el-option label="GET" value="get"> </el-option>
      <el-option label="POST" value="post"> </el-option>
    </el-select>
    <div style="text-align: right; margin-top: 5px">
      <!-- <el-button
        type="text"
        @click="
          handlevCopyEnterValue2Search('interfaceAddress')
        "
        v-if="hasCopyButton"
      >
        复制
      </el-button> -->
      <el-button
        type="text"
        @click="handleGetKVList('interfaceAddress')"
      >
        获取数据
      </el-button>
    </div>
  </el-form-item>
</template>

<script>
import i18n from '@/utils/i18n'
import settingRefMixinDesign from '../../settingRefMixinDesign'
import selectHandlerMixin from '../../selectHandlerMixin'
export default {
  inject: ['refList'],
  name: 'interfaceAddressRequestMethod-editor',
  mixins: [settingRefMixinDesign, selectHandlerMixin, i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object
  },
  computed: {
    interfaceAddressRequestMethod: {
      get() {
        // if (
        //   this.optionModel.optionSettingTabType === 'enter'
        // ) {
        //   return this.optionModel
        //     .interfaceAddressRequestMethod
        // } else if (
        //   this.optionModel.optionSettingTabType === 'search'
        // ) {
        //   return this.optionModel
        //     .searchInterfaceAddressRequestMethod
        // } else {
        //   return this.optionModel
        //     .interfaceAddressRequestMethod
        // }
        return this.optionModel
          .interfaceAddressRequestMethod
      },
      set(value) {
        // if (
        //   this.optionModel.optionSettingTabType === 'enter'
        // ) {
        //   this.optionModel.interfaceAddressRequestMethod =
        //     value
        // } else if (
        //   this.optionModel.optionSettingTabType === 'search'
        // ) {
        //   this.optionModel.searchInterfaceAddressRequestMethod =
        //     value
        // } else {
        //   this.optionModel.interfaceAddressRequestMethod =
        //     value
        // }
        this.optionModel.interfaceAddressRequestMethod =
          value
      }
    },
    hasConfig() {
      return (
        this.optionModel.optionSettingType ===
        'interfaceAddress'
      )
      // if (
      //   this.optionModel.optionSettingTabType === 'enter'
      // ) {
      //   return (
      //     this.optionModel.optionSettingType ===
      //     'interfaceAddress'
      //   )
      // } else if (
      //   this.optionModel.optionSettingTabType === 'search'
      // ) {
      //   return (
      //     this.optionModel.searchOptionSettingType ===
      //     'interfaceAddress'
      //   )
      // } else {
      //   return false
      // }
    },
    authorization() {
      return localStorage.getItem('BearToken')
    }
  }
}
</script>

<style scoped></style>
