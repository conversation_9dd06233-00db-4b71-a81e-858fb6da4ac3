<template>
  <el-form-item label="value 别名" v-if="hasConfig">
    <el-select
      filterable
      v-model="dataMarketResponseValueKey"
      @change="handleInitSelectOptionItems"
      clearable
      :placeholder="
        i18nt(
          'designer.setting.dataMarketResponseValueKeyPlaceholder'
        )
      "
      style="width: 100%"
    >
      <el-option
        v-for="(item, index) in responseKVList"
        :label="item.label"
        :value="item.value"
        :key="index"
      >
      </el-option>
    </el-select>
  </el-form-item>
</template>

<script>
import i18n from '@/utils/i18n'
import settingRefMixinDesign from '../../settingRefMixinDesign'
import selectHandlerMixin from '../../selectHandlerMixin'
export default {
  name: 'dataMarketResponseValueKey-editor',
  mixins: [settingRefMixinDesign, selectHandlerMixin, i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object
  },
  computed: {
    dataMarketResponseValueKey: {
      get() {
        // if (
        //   this.optionModel.optionSettingTabType === 'enter'
        // ) {
        //   return this.optionModel.dataMarketResponseValueKey
        // } else if (
        //   this.optionModel.optionSettingTabType === 'search'
        // ) {
        //   return this.optionModel
        //     .searchDataMarketResponseValueKey
        // } else {
        //   return this.optionModel.dataMarketResponseValueKey
        // }
        return this.optionModel.dataMarketResponseValueKey
      },
      set(value) {
        // if (
        //   this.optionModel.optionSettingTabType === 'enter'
        // ) {
        //   this.optionModel.dataMarketResponseValueKey =
        //     value
        // } else if (
        //   this.optionModel.optionSettingTabType === 'search'
        // ) {
        //   this.optionModel.searchDataMarketResponseValueKey =
        //     value
        // } else {
        //   this.optionModel.dataMarketResponseValueKey =
        //     value
        // }
        this.optionModel.dataMarketResponseValueKey = value
      }
    },
    hasConfig() {
      return (
        this.optionModel.optionSettingType === 'dataMarket'
      )
      // if (
      //   this.optionModel.optionSettingTabType === 'enter'
      // ) {
      //   return (
      //     this.optionModel.optionSettingType ===
      //     'dataMarket'
      //   )
      // } else if (
      //   this.optionModel.optionSettingTabType === 'search'
      // ) {
      //   return (
      //     this.optionModel.searchOptionSettingType ===
      //     'dataMarket'
      //   )
      // } else {
      //   return false
      // }
    }
  }
}
</script>
<style scoped></style>
