<template>
  <el-form-item label="请求参数" v-if="hasConfig">
    <el-input
      type="textarea"
      v-model="dataMarketRequestParams"
      :rows="10"
      :placeholder="
        i18nt(
          'designer.setting.dataMarketRequestParamsPlaceholder'
        )
      "
    ></el-input>
  </el-form-item>
</template>

<script>
import i18n from '@/utils/i18n'
import settingRefMixinDesign from '../../settingRefMixinDesign'
export default {
  inject: ['refList'],
  name: 'dataMarketRequestParams-editor',
  mixins: [settingRefMixinDesign, i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object
  },
  computed: {
    dataMarketRequestParams: {
      get() {
        // if (
        //   this.optionModel.optionSettingTabType === 'enter'
        // ) {
        //   return this.optionModel.dataMarketRequestParams
        // } else if (
        //   this.optionModel.optionSettingTabType === 'search'
        // ) {
        //   return this.optionModel
        //     .searchDataMarketRequestParams
        // } else {
        //   return this.optionModel.dataMarketRequestParams
        // }
        return this.optionModel.dataMarketRequestParams
      },
      set(value) {
        // if (
        //   this.optionModel.optionSettingTabType === 'enter'
        // ) {
        //   this.optionModel.dataMarketRequestParams = value
        // } else if (
        //   this.optionModel.optionSettingTabType === 'search'
        // ) {
        //   this.optionModel.searchDataMarketRequestParams =
        //     value
        // } else {
        //   this.optionModel.dataMarketRequestParams = value
        // }
        this.optionModel.dataMarketRequestParams = value
      }
    },
    hasConfig() {
      return (
        this.optionModel.optionSettingType === 'dataMarket'
      )
      // if (
      //   this.optionModel.optionSettingTabType === 'enter'
      // ) {
      //   return (
      //     this.optionModel.optionSettingType ===
      //     'dataMarket'
      //   )
      // } else if (
      //   this.optionModel.optionSettingTabType === 'search'
      // ) {
      //   return (
      //     this.optionModel.searchOptionSettingType ===
      //     'dataMarket'
      //   )
      // } else {
      //   return false
      // }
    }
  }
}
</script>
<style scoped></style>
