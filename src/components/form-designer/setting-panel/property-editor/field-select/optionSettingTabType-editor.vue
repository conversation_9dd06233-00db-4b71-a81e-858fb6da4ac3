<template>
  <el-form-item label-width="0">
    <el-tabs v-model="optionModel.optionSettingTabType">
      <el-tab-pane label="录入" name="enter"></el-tab-pane>
      <el-tab-pane label="查询" name="search"></el-tab-pane>
    </el-tabs>
  </el-form-item>
</template>

<script>
import i18n from '@/utils/i18n'
import settingRefMixinDesign from '../../settingRefMixinDesign'
import selectHandlerMixin from '../../selectHandlerMixin'
export default {
  inject: ['refList'],
  name: 'optionSettingTabType-editor',
  mixins: [settingRefMixinDesign, selectHandlerMixin, i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object
  }
}
</script>

<style scoped></style>
