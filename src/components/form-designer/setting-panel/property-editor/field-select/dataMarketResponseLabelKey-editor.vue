<template>
  <el-form-item label="label别名" v-if="hasConfig">
    <el-select
      filterable
      v-model="dataMarketResponseLabelKey"
      @change="handleInitSelectOptionItems"
      clearable
      :placeholder="
        i18nt(
          'designer.setting.dataMarketResponseLabelKeyPlaceholder'
        )
      "
      style="width: 100%"
    >
      <el-option
        v-for="(item, index) in responseKVList"
        :label="item.label"
        :value="item.value"
        :key="index"
      >
      </el-option>
    </el-select>
  </el-form-item>
</template>

<script>
import i18n from '@/utils/i18n'
import settingRefMixinDesign from '../../settingRefMixinDesign'
import selectHandlerMixin from '../../selectHandlerMixin'
export default {
  name: 'dataMarketResponseLabelKey-editor',
  mixins: [settingRefMixinDesign, selectHandlerMixin, i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object
  },
  computed: {
    dataMarketResponseLabelKey: {
      get() {
        // if (
        //   this.optionModel.optionSettingTabType === 'enter'
        // ) {
        //   return this.optionModel.dataMarketResponseLabelKey
        // } else if (
        //   this.optionModel.optionSettingTabType === 'search'
        // ) {
        //   return this.optionModel
        //     .searchDataMarketResponseLabelKey
        // } else {
        //   return this.optionModel.dataMarketResponseLabelKey
        // }
        return this.optionModel.dataMarketResponseLabelKey
      },
      set(value) {
        // if (
        //   this.optionModel.optionSettingTabType === 'enter'
        // ) {
        //   this.optionModel.dataMarketResponseLabelKey =
        //     value
        // } else if (
        //   this.optionModel.optionSettingTabType === 'search'
        // ) {
        //   this.optionModel.searchDataMarketResponseLabelKey =
        //     value
        // } else {
        //   this.optionModel.dataMarketResponseLabelKey =
        //     value
        // }
        this.optionModel.dataMarketResponseLabelKey = value
      }
    },
    hasConfig() {
      return (
        this.optionModel.optionSettingType === 'dataMarket'
      )
      // if (
      //   this.optionModel.optionSettingTabType === 'enter'
      // ) {
      //   return (
      //     this.optionModel.optionSettingType ===
      //     'dataMarket'
      //   )
      // } else if (
      //   this.optionModel.optionSettingTabType === 'search'
      // ) {
      //   return (
      //     this.optionModel.searchOptionSettingType ===
      //     'dataMarket'
      //   )
      // } else {
      //   return false
      // }
    }
  }
}
</script>
<style scoped></style>
