<template>
  <el-form-item label="key别名" v-if="hasConfig">
    <el-select
      filterable
      v-model="interfaceAddressResponseLabelKey"
      clearable
      @change="handleInitSelectOptionItems"
      :placeholder="
        i18nt(
          'designer.setting.interfaceAddressResponseLabeKeyPlaceholder'
        )
      "
      style="width: 100%"
    >
      <el-option
        v-for="(item, index) in responseKVList"
        :label="item.label"
        :value="item.value"
        :key="index"
      >
      </el-option>
    </el-select>
  </el-form-item>
</template>

<script>
import i18n from '@/utils/i18n'
import settingRefMixinDesign from '../../settingRefMixinDesign'
import selectHandlerMixin from '../../selectHandlerMixin'
export default {
  name: 'interfaceAddressResponseLabelKey-editor',
  mixins: [settingRefMixinDesign, selectHandlerMixin, i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object
  },
  computed: {
    interfaceAddressResponseLabelKey: {
      get() {
        // if (
        //   this.optionModel.optionSettingTabType === 'enter'
        // ) {
        //   return this.optionModel
        //     .interfaceAddressResponseLabelKey
        // } else if (
        //   this.optionModel.optionSettingTabType === 'search'
        // ) {
        //   return this.optionModel
        //     .searchInterfaceAddressResponseLabelKey
        // } else {
        //   return this.optionModel
        //     .interfaceAddressResponseLabelKey
        // }
        return this.optionModel
          .interfaceAddressResponseLabelKey
      },
      set(value) {
        // if (
        //   this.optionModel.optionSettingTabType === 'enter'
        // ) {
        //   this.optionModel.interfaceAddressResponseLabelKey =
        //     value
        // } else if (
        //   this.optionModel.optionSettingTabType === 'search'
        // ) {
        //   this.optionModel.searchInterfaceAddressResponseLabelKey =
        //     value
        // } else {
        //   this.optionModel.interfaceAddressResponseLabelKey =
        //     value
        // }
        return (this.optionModel.interfaceAddressResponseLabelKey =
          value)
      }
    },
    hasConfig() {
      return (
        this.optionModel.optionSettingType ===
        'interfaceAddress'
      )
      // if (
      //   this.optionModel.optionSettingTabType === 'enter'
      // ) {
      //   return (
      //     this.optionModel.optionSettingType ===
      //     'interfaceAddress'
      //   )
      // } else if (
      //   this.optionModel.optionSettingTabType === 'search'
      // ) {
      //   return (
      //     this.optionModel.searchOptionSettingType ===
      //     'interfaceAddress'
      //   )
      // } else {
      //   return false
      // }
    }
  }
}
</script>

<style scoped></style>
