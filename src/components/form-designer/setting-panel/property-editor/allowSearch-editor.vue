<template>
  <el-form-item
    :label="i18nt('designer.setting.allowSearch')"
  >
    <el-switch
      v-model="optionModel.allowSearch"
    ></el-switch>
  </el-form-item>
</template>

<script>
import i18n from '@/utils/i18n'

import settingRefMixinDesign from '../settingRefMixinDesign'
export default {
  name: 'allowSearch-editor',
  mixins: [settingRefMixinDesign, i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object
  }
}
</script>

<style scoped></style>
