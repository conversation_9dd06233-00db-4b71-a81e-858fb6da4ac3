<template>
  <el-form-item
    :label="i18nt('designer.setting.hiddenInTable')"
  >
    <el-switch
      v-model="optionModel.hiddenInTable"
    ></el-switch>
  </el-form-item>
</template>

<script>
import i18n from '@/utils/i18n'
import settingRefMixinDesign from '../settingRefMixinDesign'
export default {
  name: 'hidden-table-editor',
  mixins: [settingRefMixinDesign, i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object
  }
}
</script>

<style scoped></style>
