<template>
  <el-form-item label="行为">
    <el-card
      v-for="action in optionModel.actions"
      :key="action.id"
    >
      <el-select
        v-model="action.type"
        placeholder="请选择行为"
      >
        <el-option label="显示" value="show"></el-option>
        <el-option label="隐藏" value="hide"></el-option>
      </el-select>
      <el-input
        :rows="10"
        v-model="action.condition"
        type="textarea"
        placeholder="请输入条件"
      ></el-input>
    </el-card>
    <div>
      <el-button
        v-if="optionModel.actions.length > 0"
        type="primary"
        @click="clearActions"
        >清空</el-button
      >
      <el-button type="primary" @click="addAction"
        >添加行为</el-button
      >
    </div>
  </el-form-item>
</template>

<script>
import i18n from '@/utils/i18n'
import settingRefMixinDesign from '../settingRefMixinDesign'

export default {
  name: 'actions-editor',
  mixins: [settingRefMixinDesign, i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object
  },
  created() {
    if (!this.optionModel.actions) {
      this.$set(this.optionModel, 'actions', [])
    }
  },
  methods: {
    addAction() {
      this.optionModel.actions.push({
        id: new Date().getTime(),
        type: 'show',
        condition: ''
      })
    },
    clearActions() {
      this.optionModel.actions = []
    }
  }
}
</script>

<style lang="scss" scoped></style>
