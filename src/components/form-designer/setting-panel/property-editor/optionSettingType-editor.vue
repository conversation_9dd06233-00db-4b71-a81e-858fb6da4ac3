<template>
  <el-form-item label-width="0">
    <el-form-item
      label="配置项类型"
      v-if="selectedWidget.type === 'select'"
    >
      <el-select
        @change="handleInitSelectOptionItems"
        v-model="optionSettingType"
        style="width: 100%"
      >
        <el-option label="固定值" value="fixedValue">
        </el-option>
        <el-option
          label="接口地址"
          value="interfaceAddress"
        >
        </el-option>
        <el-option label="数据市场" value="dataMarket">
        </el-option>
      </el-select>
    </el-form-item>
    <el-form-item label-width="0">
      <option-items-setting
        :selectedWidget="selectedWidget"
        :designer="designer"
        :optionModel="optionModel"
      ></option-items-setting>
    </el-form-item>
  </el-form-item>
</template>

<script>
import OptionItemsSetting from '../option-items-setting.vue'
import i18n from '@/utils/i18n'
import settingRefMixinDesign from '../settingRefMixinDesign'
import selectHandlerMixin from '../selectHandlerMixin'
export default {
  inject: ['refList'],
  name: 'optionSettingType-editor',
  mixins: [settingRefMixinDesign, selectHandlerMixin, i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object
  },
  components: {
    OptionItemsSetting
  },
  computed: {
    optionSettingType: {
      get() {
        // if (
        //   this.optionModel.optionSettingTabType === 'enter'
        // ) {
        //   return this.optionModel.optionSettingType
        // } else if (
        //   this.optionModel.optionSettingTabType === 'search'
        // ) {
        //   return this.optionModel.searchOptionSettingType
        // } else {
        //   return this.optionModel.optionSettingType
        // }
        return this.optionModel.optionSettingType
      },
      set(value) {
        // if (
        //   this.optionModel.optionSettingTabType === 'enter'
        // ) {
        //   this.optionModel.optionSettingType = value
        // } else if (
        //   this.optionModel.optionSettingTabType === 'search'
        // ) {
        //   this.optionModel.searchOptionSettingType = value
        // } else {
        //   this.optionModel.optionSettingType = value
        // }
        this.optionModel.optionSettingType = value
      }
    }
  }
}
</script>

<style scoped></style>
