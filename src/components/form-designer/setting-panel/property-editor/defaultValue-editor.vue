<template>
  <el-form-item
    v-if="!hasConfig('optionItems')"
    :label="i18nt('designer.setting.defaultValue')"
  >
    <el-input
      v-if="!defaultValueTypeIsArray"
      type="text"
      v-model="defaultValue"
      @change="emitDefaultValueChange"
    ></el-input>
    <el-input
      rows="10"
      v-else
      type="textarea"
      v-model="defaultValue"
      @change="emitDefaultValueChange"
    ></el-input>
  </el-form-item>
</template>

<script>
import i18n from '@/utils/i18n'
import propertyMixin from '@/components/form-designer/setting-panel/property-editor/propertyMixin'
import settingRefMixinDesign from '../settingRefMixinDesign'
export default {
  name: 'defaultValue-editor',
  mixins: [settingRefMixinDesign, i18n, propertyMixin],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object
  },
  data() {
    return {
      defaultValueTypeIsArray: false
    }
  },
  computed: {
    defaultValue: {
      get() {
        const defaultValue = this.optionModel.defaultValue
        try {
          if (Array.isArray(defaultValue)) {
            if (defaultValue.length > 0) {
              return JSON.stringify(defaultValue)
            } else {
              return ''
            }
          } else {
            return defaultValue
          }
        } catch (error) {
          console.log(error)
        }
      },
      set(value) {
        try {
          if (this.defaultValueTypeIsArray) {
            if (value) {
              this.optionModel.defaultValue =
                JSON.parse(value)
            } else {
              this.optionModel.defaultValue = []
            }
          } else {
            this.optionModel.defaultValue = value
          }
        } catch (error) {
          console.log(error)
        }
      }
    }
  },
  created() {
    const defaultValue = this.optionModel.defaultValue
    if (Array.isArray(defaultValue)) {
      this.defaultValueTypeIsArray = true
    } else {
      this.defaultValueTypeIsArray = false
    }
  }
}
</script>

<style scoped></style>
