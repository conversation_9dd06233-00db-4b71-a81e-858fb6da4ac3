<template>
  <div label-width="0" class="option-items-pane">
    <!-- optionModel.defaultValue -->
    <el-radio-group
      v-if="hasRadioGroup"
      v-model="defaultValue"
      @change="emitDefaultValueChange"
    >
      <draggable
        tag="ul"
        :list="optionItems"
        v-bind="{
          group: 'optionsGroup',
          ghostClass: 'ghost',
          handle: '.drag-option'
        }"
      >
        <li v-for="(option, idx) in optionItems" :key="idx">
          <el-radio :label="option.value">
            <el-input
              v-model="option.value"
              size="mini"
              style="width: 100px"
            ></el-input>
            <el-input
              v-model="option.label"
              size="mini"
              style="width: 100px"
            ></el-input>
            <i class="iconfont icon-drag drag-option"></i>
            <el-button
              circle
              plain
              size="mini"
              type="danger"
              @click="deleteOption(option, idx)"
              icon="el-icon-minus"
              class="col-delete-button"
            ></el-button>
          </el-radio>
        </li>
      </draggable>
    </el-radio-group>
    <!-- optionModel.defaultValue -->
    <el-checkbox-group
      v-else-if="hasCheckboxGroup"
      v-model="defaultValue"
      @change="emitDefaultValueChange"
    >
      <draggable
        tag="ul"
        :list="optionItems"
        v-bind="{
          group: 'optionsGroup',
          ghostClass: 'ghost',
          handle: '.drag-option'
        }"
      >
        <li v-for="(option, idx) in optionItems" :key="idx">
          <el-checkbox :label="option.value">
            <el-input
              v-model="option.value"
              size="mini"
              style="width: 100px"
            ></el-input>
            <el-input
              v-model="option.label"
              size="mini"
              style="width: 100px"
            ></el-input>
            <i class="iconfont icon-drag drag-option"></i>
            <el-button
              circle
              plain
              size="mini"
              type="danger"
              @click="deleteOption(option, idx)"
              icon="el-icon-minus"
              class="col-delete-button"
            ></el-button>
          </el-checkbox>
        </li>
      </draggable>
    </el-checkbox-group>
    <el-cascader
      v-else-if="selectedWidget.type === 'cascader'"
      v-model="optionModel.defaultValue"
      :options="optionModel.optionItems"
      @change="emitDefaultValueChange"
      :placeholder="i18nt('render.hint.selectPlaceholder')"
      style="width: 100%"
    >
    </el-cascader>
    <div v-if="selectedWidget.type === 'cascader'">
      <el-button
        type="text"
        @click="importCascaderOptions"
        >{{
          i18nt('designer.setting.importOptions')
        }}</el-button
      >
      <el-button type="text" @click="resetDefault">{{
        i18nt('designer.setting.resetDefault')
      }}</el-button>
    </div>
    <div
      v-if="
        selectedWidget.type === 'radio' ||
        selectedWidget.type === 'checkbox' ||
        (selectedWidget.type === 'select' && hasConfig)
      "
    >
      <el-button type="text" @click="addOption">{{
        i18nt('designer.setting.addOption')
      }}</el-button>
      <el-button type="text" @click="importOptions">{{
        i18nt('designer.setting.importOptions')
      }}</el-button>
      <el-button type="text" @click="resetDefault">{{
        i18nt('designer.setting.resetDefault')
      }}</el-button>
      <!-- <el-button
        v-if="hasCopyButton"
        type="text"
        @click="handlevCopyEnterValue2Search('fixedValue')"
        >复制</el-button
      > -->
    </div>

    <el-dialog
      :title="i18nt('designer.setting.importOptions')"
      :visible.sync="showImportDialogFlag"
      v-if="showImportDialogFlag"
      :show-close="true"
      class="small-padding-dialog"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
    >
      <el-form-item>
        <el-input
          type="textarea"
          rows="10"
          v-model="optionLines"
        ></el-input>
      </el-form-item>
      <div slot="footer" class="dialog-footer">
        <el-button
          size="large"
          type="primary"
          @click="saveOptions"
          >{{ i18nt('designer.hint.confirm') }}</el-button
        >
        <el-button
          size="large"
          type=""
          @click="showImportDialogFlag = false"
          >{{ i18nt('designer.hint.cancel') }}</el-button
        >
      </div>
    </el-dialog>

    <el-dialog
      :title="i18nt('designer.setting.importOptions')"
      :visible.sync="showImportCascaderDialogFlag"
      v-if="showImportCascaderDialogFlag"
      :show-close="true"
      class="small-padding-dialog"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
    >
      <code-editor
        v-model="cascaderOptions"
        mode="json"
        :readonly="false"
      ></code-editor>
      <div slot="footer" class="dialog-footer">
        <el-button
          size="large"
          type="primary"
          @click="saveCascaderOptions"
          >{{ i18nt('designer.hint.confirm') }}</el-button
        >
        <el-button
          size="large"
          type=""
          @click="showImportCascaderDialogFlag = false"
          >{{ i18nt('designer.hint.cancel') }}</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Draggable from 'vuedraggable'
import CodeEditor from '@/components/code-editor/index'
import i18n from '@/utils/i18n'
import selectHandlerMixin from './selectHandlerMixin'
export default {
  inject: ['refList'],
  name: 'optionItemsSetting-editor',
  mixins: [selectHandlerMixin, i18n],
  components: {
    Draggable,
    CodeEditor
  },
  props: {
    designer: Object,
    selectedWidget: Object
  },
  data() {
    return {
      showImportDialogFlag: false,
      optionLines: '',
      cascaderOptions: '',
      showImportCascaderDialogFlag: false,
      separator: ','
    }
  },
  computed: {
    defaultValue: {
      get() {
        return this.optionModel.defaultValue
      },
      set(value) {
        this.optionModel.defaultValue = value
      }
    },
    hasCheckboxGroup() {
      return (
        this.selectedWidget.type === 'checkbox' ||
        (this.selectedWidget.type === 'select' &&
          this.hasConfig &&
          this.selectedWidget.options.multiple)
      )
    },
    hasRadioGroup() {
      return (
        this.selectedWidget.type === 'radio' ||
        (this.selectedWidget.type === 'select' &&
          this.hasConfig &&
          !this.selectedWidget.options.multiple)
      )
    },
    optionItems() {
      // return this.isEnter
      //   ? this.optionModel.optionItems
      //   : this.optionModel.searchOptionItems
      return this.optionModel.optionItems
    },
    hasConfig() {
      // if (
      //   this.optionModel.optionSettingTabType === 'enter'
      // ) {
      //   return (
      //     this.optionModel.optionSettingType ===
      //     'fixedValue'
      //   )
      // } else if (
      //   this.optionModel.optionSettingTabType === 'search'
      // ) {
      //   return (
      //     this.optionModel.searchOptionSettingType ===
      //     'fixedValue'
      //   )
      // } else {
      //   return false
      // }
      return (
        this.optionModel.optionSettingType === 'fixedValue'
      )
    }
    // isEnter() {
    //   return (
    //     this.optionModel.optionSettingTabType === 'enter'
    //   )
    // }
  },
  watch: {
    optionItems: {
      deep: true,
      handler() {
        if (this.selectedWidget.type === 'select') {
          const selectWidgetRef =
            this.refList[this.selectedWidget.options.name]
          if (selectWidgetRef) {
            selectWidgetRef.initSelectOptionItems(false)
          }
        }
      }
    }
  },
  methods: {
    emitDefaultValueChange() {
      if (!!this.designer && !!this.designer.formWidget) {
        const fieldWidget =
          this.designer.formWidget.getWidgetRef(
            this.selectedWidget.options.name
          )
        if (
          !!fieldWidget &&
          !!fieldWidget.refreshDefaultValue
        ) {
          fieldWidget.refreshDefaultValue()
        }
      }
    },

    deleteOption(_option, index) {
      // if (this.isEnter) {
      //   this.optionModel.optionItems.splice(index, 1)
      // } else {
      //   this.optionModel.searchOptionItems.splice(index, 1)
      // }
      this.optionModel.optionItems.splice(index, 1)
    },
    /**
     * 添加选项
     */
    addOption() {
      const newValue =
        this.optionModel.optionItems.length + 1
      // if (this.isEnter) {
      //   this.optionModel.optionItems.push({
      //     value: newValue,
      //     label: 'new option'
      //   })
      // } else {
      //   this.optionModel.searchOptionItems.push({
      //     value: newValue,
      //     label: 'new option'
      //   })
      // }
      this.optionModel.optionItems.push({
        value: newValue,
        label: 'new option'
      })
    },
    /**
     * 导入选项
     */
    importOptions() {
      this.optionLines = ''
      // if (this.isEnter) {
      //   if (this.optionModel.optionItems.length > 0) {
      //     this.optionModel.optionItems.forEach((opt) => {
      //       if (opt.value === opt.label) {
      //         this.optionLines += opt.value + '\n'
      //       } else {
      //         this.optionLines +=
      //           opt.value +
      //           this.separator +
      //           opt.label +
      //           '\n'
      //       }
      //     })
      //   }
      // } else {
      //   if (this.optionModel.searchOptionItems.length > 0) {
      //     this.optionModel.searchOptionItems.forEach(
      //       (opt) => {
      //         if (opt.value === opt.label) {
      //           this.optionLines += opt.value + '\n'
      //         } else {
      //           this.optionLines +=
      //             opt.value +
      //             this.separator +
      //             opt.label +
      //             '\n'
      //         }
      //       }
      //     )
      //   }
      // }
      if (this.optionModel.optionItems.length > 0) {
        this.optionModel.optionItems.forEach((opt) => {
          if (opt.value === opt.label) {
            this.optionLines += opt.value + '\n'
          } else {
            this.optionLines +=
              opt.value + this.separator + opt.label + '\n'
          }
        })
      }
      this.showImportDialogFlag = true
    },
    /**
     * 保存导入选项
     */
    saveOptions() {
      const lineArray = this.optionLines.split('\n')
      if (lineArray.length > 0) {
        // if (this.isEnter) {
        this.optionModel.optionItems = []
        // } else {
        //   this.optionModel.searchOptionItems = []
        // }
        lineArray.forEach((optLine) => {
          if (!!optLine && !!optLine.trim()) {
            if (optLine.indexOf(this.separator) !== -1) {
              // if (this.isEnter) {
              this.optionModel.optionItems.push({
                value: optLine.split(this.separator)[0],
                label: optLine.split(this.separator)[1]
              })
              // } else {
              //   this.optionModel.searchOptionItems.push({
              //     value: optLine.split(this.separator)[0],
              //     label: optLine.split(this.separator)[1]
              //   })
              // }
            } else {
              // if (this.isEnter) {
              this.optionModel.optionItems.push({
                value: optLine,
                label: optLine
              })
              // } else {
              //   this.optionModel.searchOptionItems.push({
              //     value: optLine,
              //     label: optLine
              //   })
              // }
            }
          }
        })
      } else {
        // if (this.isEnter) {
        this.optionModel.optionItems = []
        // } else {
        //   this.optionModel.searchOptionItems = []
        // }
      }

      this.showImportDialogFlag = false
    },
    /**
     * 重置默认值
     */
    resetDefault() {
      if (
        this.selectedWidget.type === 'checkbox' ||
        (this.selectedWidget.type === 'select' &&
          this.selectedWidget.options.multiple)
      ) {
        // if (this.isEnter) {
        this.optionModel.defaultValue = []
        // } else {
        //   this.optionModel.searchDefaultValue = []
        // }
      } else {
        // if (this.isEnter) {
        this.optionModel.defaultValue = ''
        // } else {
        //   this.optionModel.searchDefaultValue = ''
        // }
      }
      this.emitDefaultValueChange()
    },

    importCascaderOptions() {
      this.cascaderOptions = JSON.stringify(
        this.optionModel.optionItems,
        null,
        '  '
      )
      this.showImportCascaderDialogFlag = true
    },

    saveCascaderOptions() {
      try {
        let newOptions = JSON.parse(this.cascaderOptions)
        this.optionModel.optionItems = newOptions
        //TODO: 是否需要重置选项默认值？？

        this.showImportCascaderDialogFlag = false
      } catch (ex) {
        this.$message.error(
          this.i18nt('designer.hint.invalidOptionsData') +
            ex.message
        )
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.option-items-pane ul {
  padding-inline-start: 6px;
  padding-left: 6px;
  /* 重置IE11默认样式 */
}

li.ghost {
  background: #fff;
  border: 2px dotted $--color-primary;
}

.drag-option {
  cursor: move;
}

.small-padding-dialog ::v-deep .el-dialog__body {
  padding: 10px 15px;
}

.dialog-footer .el-button {
  width: 100px;
}
</style>
