export default {
  inject: ['settingRefList'],
  methods: {
    initSettingRefList() {
      this.$nextTick(() => {
        const settingRefName = this.$options.name
        if (
          this.settingRefList !== null &&
          !!settingRefName
        ) {
          this.settingRefList[settingRefName] = this
        }
      })
    },

    getSettingRef(settingRefName, showError = false) {
      const settingRef = this.settingRefList[settingRefName]
      if (!settingRef && !!showError) {
        this.$message.error(settingRefName + '组件未注册')
      }
      return settingRef
    }
  },
  created() {
    this.initSettingRefList()
  },
  beforeDestroy() {
    this.settingRefList[this.$options.name] = null
  }
}
