<template>
  <el-container class="panel-container">
    <el-tabs
      v-model="settingActiveTabName"
      style="height: 100%; overflow: hidden; width: 100%"
    >
      <!-- 组件设置 -->
      <el-tab-pane
        :label="i18nt('designer.hint.widgetSetting')"
        name="1"
      >
        <el-scrollbar1
          class="setting-scrollbar"
          style="height: 100%; padding-bottom: 50px"
        >
          <template
            v-if="
              !!designer.selectedWidget &&
              !designer.selectedWidget.category &&
              settingActiveTabName === '1'
            "
          >
            <!-- 表单组件配置 -->
            <el-form
              :model="optionModel"
              size="mini"
              label-position="left"
              label-width="120px"
              class="setting-form"
              @submit.native.prevent
            >
              <el-collapse
                v-model="widgetActiveCollapseNames"
                class="setting-collapse"
              >
                <!-- 表单组件配置常见属性 -->
                <el-collapse-item
                  name="1"
                  v-if="showCollapse(commonProps)"
                  :title="
                    i18nt('designer.setting.commonSetting')
                  "
                >
                  <template
                    v-for="(
                      editorName, propName
                    ) in commonProps"
                  >
                    <component
                      v-if="
                        hasPropEditor(propName, editorName)
                      "
                      :key="selectedWidget.id + propName"
                      :is="
                        getPropEditor(propName, editorName)
                      "
                      :designer="designer"
                      :selected-widget="selectedWidget"
                      :option-model="optionModel"
                    >
                    </component>
                  </template>
                </el-collapse-item>
                <!-- 表单组件配置高级属性 -->
                <el-collapse-item
                  name="2"
                  v-if="showCollapse(advProps)"
                  :title="
                    i18nt(
                      'designer.setting.advancedSetting'
                    )
                  "
                >
                  <template
                    v-for="(
                      editorName, propName
                    ) in advProps"
                  >
                    <component
                      v-if="
                        hasPropEditor(propName, editorName)
                      "
                      :key="selectedWidget.id + propName"
                      :is="
                        getPropEditor(propName, editorName)
                      "
                      :designer="designer"
                      :selected-widget="selectedWidget"
                      :option-model="optionModel"
                    >
                    </component>
                  </template>
                </el-collapse-item>
                <!-- 表单组件配置事件属性 -->
                <el-collapse-item
                  name="3"
                  v-if="
                    showEventCollapse() &&
                    showCollapse(eventProps)
                  "
                  :title="
                    i18nt('designer.setting.eventSetting')
                  "
                >
                  <template
                    v-for="(
                      editorName, propName
                    ) in eventProps"
                  >
                    <component
                      v-if="
                        hasPropEditor(propName, editorName)
                      "
                      :key="selectedWidget.id + propName"
                      :is="
                        getPropEditor(propName, editorName)
                      "
                      :designer="designer"
                      :selected-widget="selectedWidget"
                      :option-model="optionModel"
                    >
                    </component>
                  </template>
                </el-collapse-item>
              </el-collapse>
            </el-form>
          </template>
          <!-- 容器组件配置 -->
          <template
            v-if="
              !!designer.selectedWidget &&
              !!designer.selectedWidget.category &&
              settingActiveTabName === '1'
            "
          >
            <el-form
              :model="optionModel"
              size="mini"
              label-position="left"
              label-width="120px"
              class="setting-form"
              @submit.native.prevent
            >
              <!-- 容器组件配置常见属性 -->
              <el-collapse
                v-model="widgetActiveCollapseNames"
                class="setting-collapse"
              >
                <el-collapse-item
                  name="1"
                  v-if="showCollapse(commonProps)"
                  :title="
                    i18nt('designer.setting.commonSetting')
                  "
                >
                  <template
                    v-for="(
                      editorName, propName
                    ) in commonProps"
                  >
                    <component
                      v-if="
                        hasPropEditor(propName, editorName)
                      "
                      :key="selectedWidget.id + propName"
                      :is="
                        getPropEditor(propName, editorName)
                      "
                      :designer="designer"
                      :selected-widget="selectedWidget"
                      :option-model="optionModel"
                    >
                    </component>
                  </template>
                </el-collapse-item>
                <!-- 容器组件配置高级属性 -->
                <el-collapse-item
                  name="2"
                  v-if="showCollapse(advProps)"
                  :title="
                    i18nt(
                      'designer.setting.advancedSetting'
                    )
                  "
                >
                  <template
                    v-for="(
                      editorName, propName
                    ) in advProps"
                  >
                    <component
                      v-if="
                        hasPropEditor(propName, editorName)
                      "
                      :key="selectedWidget.id + propName"
                      :is="
                        getPropEditor(propName, editorName)
                      "
                      :designer="designer"
                      :selected-widget="selectedWidget"
                      :option-model="optionModel"
                    >
                    </component>
                  </template>
                </el-collapse-item>
                <!-- 容器组件配置事件属性 -->
                <el-collapse-item
                  name="3"
                  v-if="
                    showEventCollapse() &&
                    showCollapse(eventProps)
                  "
                  :title="
                    i18nt('designer.setting.eventSetting')
                  "
                >
                  <template
                    v-for="(
                      editorName, propName
                    ) in eventProps"
                  >
                    <component
                      v-if="
                        hasPropEditor(propName, editorName)
                      "
                      :key="selectedWidget.id + propName"
                      :is="
                        getPropEditor(propName, editorName)
                      "
                      :designer="designer"
                      :selected-widget="selectedWidget"
                      :option-model="optionModel"
                    >
                    </component>
                  </template>
                </el-collapse-item>
              </el-collapse>
            </el-form>
          </template>
        </el-scrollbar1>
      </el-tab-pane>

      <!-- 表单设置 -->
      <el-tab-pane
        style="height: 100%"
        v-if="!!designer && !parentContainerName"
        :label="i18nt('designer.hint.formSetting')"
        name="2"
      >
        <el-scrollbar1
          class="setting-scrollbar"
          style="height: 100%; padding-bottom: 50px"
        >
          <form-setting
            v-if="settingActiveTabName === '2'"
            :designer="designer"
            :form-config="formConfig"
          ></form-setting>
        </el-scrollbar1>
      </el-tab-pane>

      <!-- 搜索设置 -->
      <el-tab-pane
        :label="i18nt('designer.hint.searchSetting')"
        name="3"
      >
        <el-scrollbar1
          class="setting-scrollbar"
          style="height: 100%; padding-bottom: 50px"
        >
          <template
            v-if="
              !!designer.selectedSearchConfig &&
              settingActiveTabName === '3'
            "
          >
            <el-form
              :model="searchOptionModel"
              size="mini"
              label-position="left"
              label-width="120px"
              class="setting-form"
              @submit.native.prevent
            >
              <el-collapse
                v-model="widgetActiveCollapseNames"
                class="setting-collapse"
              >
                <el-collapse-item
                  name="1"
                  v-if="showCollapse(commonProps)"
                  :title="
                    i18nt('designer.setting.commonSetting')
                  "
                >
                  <template
                    v-for="(
                      editorName, propName
                    ) in commonProps"
                  >
                    <component
                      :search-config-flag="true"
                      v-if="
                        hasPropEditor(propName, editorName)
                      "
                      :key="
                        selectedSearchConfig.id + propName
                      "
                      :is="
                        getPropEditor(propName, editorName)
                      "
                      :designer="designer"
                      :selected-widget="
                        selectedSearchConfig
                      "
                      :option-model="searchOptionModel"
                    >
                    </component>
                  </template>
                </el-collapse-item>
                <el-collapse-item
                  name="2"
                  v-if="showCollapse(advProps)"
                  :title="
                    i18nt(
                      'designer.setting.advancedSetting'
                    )
                  "
                >
                  <template
                    v-for="(
                      editorName, propName
                    ) in advProps"
                  >
                    <component
                      :search-config-flag="true"
                      v-if="
                        hasPropEditor(propName, editorName)
                      "
                      :key="
                        selectedSearchConfig.id + propName
                      "
                      :is="
                        getPropEditor(propName, editorName)
                      "
                      :designer="designer"
                      :selected-widget="
                        selectedSearchConfig
                      "
                      :option-model="searchOptionModel"
                    >
                    </component>
                  </template>
                </el-collapse-item>
                <el-collapse-item
                  name="3"
                  v-if="
                    showEventCollapse() &&
                    showCollapse(eventProps)
                  "
                  :title="
                    i18nt('designer.setting.eventSetting')
                  "
                >
                  <template
                    v-for="(
                      editorName, propName
                    ) in eventProps"
                  >
                    <component
                      :search-config-flag="true"
                      v-if="
                        hasPropEditor(propName, editorName)
                      "
                      :key="
                        selectedSearchConfig.id + propName
                      "
                      :is="
                        getPropEditor(propName, editorName)
                      "
                      :designer="designer"
                      :selected-widget="
                        selectedSearchConfig
                      "
                      :option-model="searchOptionModel"
                    >
                    </component>
                  </template>
                </el-collapse-item>
              </el-collapse>
            </el-form>
          </template>
        </el-scrollbar1>
      </el-tab-pane>
    </el-tabs>

    <!-- 代码编辑器 -->
    <el-dialog
      :title="
        i18nt('designer.setting.editWidgetEventHandler')
      "
      :visible.sync="showWidgetEventDialogFlag"
      v-if="showWidgetEventDialogFlag"
      :show-close="true"
      class="small-padding-dialog"
      append-to-body
      v-dialog-drag
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
    >
      <el-alert
        type="info"
        :closable="false"
        :title="eventHeader"
      ></el-alert>
      <code-editor
        :mode="'javascript'"
        :readonly="false"
        v-model="eventHandlerCode"
        ref="ecEditor"
      ></code-editor>
      <el-alert
        type="info"
        :closable="false"
        title="}"
      ></el-alert>
      <div slot="footer" class="dialog-footer">
        <el-button
          @click="showWidgetEventDialogFlag = false"
        >
          {{ i18nt('designer.hint.cancel') }}</el-button
        >
        <el-button type="primary" @click="saveEventHandler">
          {{ i18nt('designer.hint.confirm') }}</el-button
        >
      </div>
    </el-dialog>
  </el-container>
</template>

<script>
import CodeEditor from '@/components/code-editor/index'
import PropertyEditors from './property-editor/index'
import FormSetting from './form-setting'
import WidgetProperties from './propertyRegister'
import { addWindowResizeHandler } from '@/utils/util'
import i18n from '@/utils/i18n'
import { propertyRegistered } from '@/components/form-designer/setting-panel/propertyRegister'
import scrollbar from '@/components/scrollbar/src/main.js'
const {
  COMMON_PROPERTIES,
  ADVANCED_PROPERTIES,
  EVENT_PROPERTIES
} = WidgetProperties

export default {
  name: 'SettingPanel',
  componentName: 'SettingPanel',
  mixins: [i18n],
  components: {
    CodeEditor,
    FormSetting,
    ...PropertyEditors,
    'el-scrollbar1': scrollbar
  },
  props: {
    designer: Object,
    selectedWidget: Object,
    selectedSearchConfig: Object,
    formConfig: Object,
    parentContainerName: {
      type: String,
      default: () => ''
    }
  },
  provide() {
    return {
      isSubFormChildWidget: () =>
        this.subFormChildWidgetFlag
    }
  },
  inject: ['getDesignerConfig'],
  data() {
    return {
      designerConfig: this.getDesignerConfig(),
      scrollerHeight: 0,
      settingActiveTabName: '2',
      widgetActiveCollapseNames: ['1', '3'], // ['1', '2', '3'],
      formActiveCollapseNames: ['1', '2'],
      commonProps: COMMON_PROPERTIES,
      advProps: ADVANCED_PROPERTIES,
      eventProps: EVENT_PROPERTIES,
      showWidgetEventDialogFlag: false,
      eventHandlerCode: '',
      curEventName: '',
      eventHeader: '',
      subFormChildWidgetFlag: false
    }
  },
  computed: {
    optionModel: {
      get() {
        return this.selectedWidget.options
      },

      set(newValue) {
        this.selectedWidget.options = newValue
      }
    },
    searchOptionModel: {
      get() {
        return this.selectedSearchConfig.options
      },
      set(newValue) {
        this.selectedSearchConfig.options = newValue
      }
    }
  },
  watch: {
    'designer.selectedWidget': {
      handler(val) {
        if (!!val && this.settingActiveTabName === '2') {
          this.settingActiveTabName = '1'
        }
      }
    },
    'selectedWidget.options': {
      // 组件属性变动后，立即保存表单JSON！！
      deep: true,
      handler() {
        this.designer.saveCurrentHistoryStep()
      }
    },
    // 搜索组件属性变动后，立即保存表单JSON！！
    'selectedSearchConfig.options': {
      // 搜索属性变动后，立即保存表单JSON！！
      deep: true,
      handler() {
        this.designer.updateSearchConfig(
          this.selectedSearchConfig
        )
        this.designer.saveCurrentHistoryStep()
      }
    },
    formConfig: {
      deep: true,
      handler() {
        this.designer.saveCurrentHistoryStep()
      }
    }
  },
  created() {
    this.$on(
      'editEventHandler',
      (eventName, eventParams) => {
        this.editEventHandler(eventName, eventParams)
      }
    )

    this.designer.handleEvent(
      'form-css-updated',
      (cssClassList) => {
        this.designer.setCssClassList(cssClassList)
      }
    )

    // 监听字段组件选中事件
    this.designer.handleEvent(
      'field-selected',
      (parentWidget) => {
        this.subFormChildWidgetFlag =
          !!parentWidget && parentWidget.type === 'sub-form'
      }
    )
  },
  mounted() {
    if (!this.designer.selectedWidget) {
      this.settingActiveTabName = '2'
    } else {
      this.settingActiveTabName = '1'
    }

    this.scrollerHeight =
      window.innerHeight - 56 - 48 + 'px'
    addWindowResizeHandler(() => {
      this.$nextTick(() => {
        this.scrollerHeight =
          window.innerHeight - 56 - 48 + 'px'
      })
    })
  },
  methods: {
    showEventCollapse() {
      if (
        this.designerConfig['eventCollapse'] === undefined
      ) {
        return true
      }

      return !!this.designerConfig['eventCollapse']
    },
    /**
     * 判断是否有该组件
     * @param propName
     * @param editorName
     */
    hasPropEditor(propName, editorName) {
      if (!editorName) return false
      /* alert组件注册了两个type属性编辑器，跳过第一个type属性编辑器，只显示第二个alert-type属性编辑器！！ */
      const type =
        this.settingActiveTabName === '1'
          ? this.selectedWidget.type
          : this.selectedSearchConfig.type

      if (!propName.includes('-')) {
        const uniquePropName = type + '-' + propName
        if (propertyRegistered(uniquePropName)) return false
      }
      // 从组件名字中获取属性名
      const config = propName.replace(type + '-', '')

      const allowShowOptionItemSetting = [
        'cascader',
        'select',
        'radio',
        'checkbox'
      ]
      if (
        allowShowOptionItemSetting.includes(type) &&
        config === 'optionSettingType'
      ) {
        return true
      }
      // 去掉组件名称前缀-，如果有的话！！
      const hasConfig = this.designer.hasConfig(
        this.settingActiveTabName === '1'
          ? this.selectedWidget
          : this.selectedSearchConfig,
        config
      )
      return hasConfig
    },

    /**
     * 加载属性编辑器组件 告诉component组件应该加载哪个组件 前置已经将 所有的组件已经加载了
     * @param propName
     * @param editorName
     */
    getPropEditor(propName, editorName) {
      const type =
        this.settingActiveTabName === '1'
          ? this.selectedWidget.type
          : this.selectedSearchConfig.type

      const originalPropName = propName.replace(
        type + '-',
        ''
      ) // 去掉组件名称前缀-，如果有的话！！

      const ownPropEditorName = `${type}-${originalPropName}-editor`

      if (!!this.$options.components[ownPropEditorName]) {
        // 局部注册的属性编辑器组件
        return ownPropEditorName
      }

      return !!this.$root.$options.components[
        ownPropEditorName
      ]
        ? ownPropEditorName
        : editorName // 全局注册的属性编辑器组件
    },

    showCollapse(propsObj) {
      let result = false

      for (let propName in propsObj) {
        if (!propsObj.hasOwnProperty(propName)) {
          continue
        }

        if (
          this.hasPropEditor(propName, propsObj[propName])
        ) {
          result = true
          break
        }
      }

      return result
    },

    editEventHandler(eventName, eventParams) {
      this.curEventName = eventName
      this.eventHeader = `${
        this.optionModel.name
      }.${eventName}(${eventParams.join(', ')}) {`
      this.eventHandlerCode =
        this.selectedWidget.options[eventName] || ''

      // 设置字段校验函数示例代码
      if (
        eventName === 'onValidate' &&
        !this.optionModel['onValidate']
      ) {
        this.eventHandlerCode =
          "  /* sample code */\n  /*\n  if ((value > 100) || (value < 0)) {\n    callback(new Error('error message'))  //fail\n  } else {\n    callback();  //pass\n  }\n  */"
      }

      this.showWidgetEventDialogFlag = true
    },

    saveEventHandler() {
      const codeHints =
        this.$refs.ecEditor.getEditorAnnotations()
      let syntaxErrorFlag = false
      if (!!codeHints && codeHints.length > 0) {
        codeHints.forEach((chItem) => {
          if (chItem.type === 'error') {
            syntaxErrorFlag = true
          }
        })

        if (syntaxErrorFlag) {
          this.$message.error(
            this.i18nt(
              'designer.setting.syntaxCheckWarning'
            )
          )
          return
        }
      }

      this.selectedWidget.options[this.curEventName] =
        this.eventHandlerCode
      this.showWidgetEventDialogFlag = false
    }
  }
}
</script>

<style lang="scss" scoped>
.panel-container {
  padding: 0 8px;
  width: 100%;
  height: 100%;

  ::v-deep .el-tabs {
    .el-tabs__content {
      height: 100%;
      .el-tab-pane {
        height: 100%;
      }
    }
  }
}

.setting-scrollbar {
  ::v-deep .el-scrollbar__wrap {
    padding-right: 10px;
    overflow-x: hidden;
    /* IE浏览器隐藏水平滚动条箭头！！ */
  }
}

.setting-collapse {
  ::v-deep .el-collapse-item__content {
    padding-bottom: 6px;
  }

  ::v-deep .el-collapse-item__header {
    font-style: italic;
    font-weight: bold;
  }
}

.setting-form {
  ::v-deep .el-form-item__label {
    font-size: 13px;
    //text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }

  ::v-deep .el-form-item--mini.el-form-item {
    margin-bottom: 6px;
  }
}

/* 隐藏Chrome浏览器中el-input数字输入框右侧的上下调整小箭头 */
::v-deep .hide-spin-button input::-webkit-outer-spin-button,
::v-deep
  .hide-spin-button
  input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}

/* 隐藏Firefox浏览器中el-input数字输入框右侧的上下调整小箭头 */
// ::v-deep .hide-spin-button input[type='number'] {
//   -moz-appearance: textfield;
// }

::v-deep .custom-divider.el-divider--horizontal {
  margin: 10px 0;
}

::v-deep .custom-divider-margin-top.el-divider--horizontal {
  margin: 20px 0;
}

.small-padding-dialog {
  ::v-deep .el-dialog__body {
    padding: 6px 15px 12px 15px;
  }
}

::v-deep .el-scrollbar__wrap1 {
  overflow-y: auto;
  height: 100%;
}

::v-deep .el-scrollbar__wrap1::-webkit-scrollbar {
  width: 15px !important;
}
</style>
