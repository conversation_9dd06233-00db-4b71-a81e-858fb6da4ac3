<template>
  <form-item-wrapper
    :designer="designer"
    :field="field"
    :rules="rules"
    :design-state="designState"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
    :sub-form-row-index="subFormRowIndex"
    :sub-form-col-index="subFormColIndex"
    :sub-form-row-id="subFormRowId"
    v-if="showField"
  >
    <el-date-picker
      ref="fieldEditor"
      :type="field.options.type"
      v-model="fieldModel"
      :popper-class="isMobile ? 'mobile-date-picker' : ''"
      class="full-width-input"
      :readonly="field.options.readonly"
      :disabled="field.options.disabled"
      :size="field.options.size"
      :clearable="field.options.clearable"
      :editable="field.options.editable"
      :format="field.options.format"
      :value-format="field.options.valueFormat"
      :placeholder="
        field.options.placeholder ||
        i18nt('render.hint.datePlaceholder')
      "
      @focus="handleFocusCustomEvent"
      @blur="handleBlurCustomEvent"
      @change="handleChangeEvent"
      :picker-options="pickerOptions"
    >
    </el-date-picker>
  </form-item-wrapper>
</template>

<script>
import FormItemWrapper from './form-item-wrapper'
import isMobile from '@/utils/isMobile'
import emitter from '@/utils/emitter'
import i18n from '@/utils/i18n'
import fieldMixin from '@/components/form-designer/form-widget/field-widget/fieldMixin'
import computedSettingMixins from './computedSettingMixins'
import actionsHandlerMixin from './actionsHandlerMixin'
import dayjs from 'dayjs'
export default {
  name: 'date-widget',
  componentName: 'FieldWidget', //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  mixins: [
    isMobile,
    emitter,
    fieldMixin,
    computedSettingMixins,
    i18n,
    actionsHandlerMixin
  ],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,
    designState: {
      type: Boolean,
      default: false
    },
    subFormRowIndex: {
      /* 子表单组件行索引，从0开始计数 */
      type: Number,
      default: -1
    },
    subFormColIndex: {
      /* 子表单组件列索引，从0开始计数 */
      type: Number,
      default: -1
    },
    subFormRowId: {
      /* 子表单组件行Id，唯一id且不可变 */
      type: String,
      default: ''
    }
  },
  components: {
    FormItemWrapper
  },
  data() {
    return {
      oldFieldValue: null, // field组件change之前的值

      rules: []
    }
  },
  computed: {
    pickerOptions() {
      return {
        disabledDate: (time) => {
          const earliestAvailableTimeSwitch =
            this.field.options.earliestAvailableTimeSwitch
          const earliestAvailableTimeType =
            this.field.options.earliestAvailableTimeType
          const earliestAvailableTime =
            this.field.options.earliestAvailableTime
          const minDayCount = earliestAvailableTimeSwitch
            ? earliestAvailableTime
            : -10000
          // 获取当前日期范围
          const minDay = dayjs()
            .subtract(Math.abs(minDayCount), 'day')
            .startOf('day') // 最小日期
          const latestAvailableTimeSwitch =
            this.field.options.latestAvailableTimeSwitch
          const latestAvailableTimeType =
            this.field.options.latestAvailableTimeType
          const latestAvailableTime =
            this.field.options.latestAvailableTime
          const maxDayCount = latestAvailableTimeSwitch
            ? latestAvailableTime
            : 10000
          const maxDay = dayjs()
            .add(maxDayCount, 'day')
            .endOf('day') // 最大日期
          const currentTime = dayjs(time) // 转换 time 为 dayjs 对象
          // 禁用范围外的日期
          return (
            currentTime.isBefore(minDay) ||
            currentTime.isAfter(maxDay)
          )
        }
      }
    }
  },
  created() {
    this.initFieldModel()
    this.registerToRefList()
    this.initEventHandler()
    this.buildFieldRules()
    this.handleOnCreated()
  },
  mounted() {
    this.handleOnMounted()
  },
  beforeDestroy() {
    this.unregisterFromRefList()
  }
}
</script>

<style lang="scss" scoped>
@import '../../../../styles/global.scss';

.full-width-input {
  width: 100% !important;
}
</style>
