<template>
  <form-item-wrapper
    :designer="designer"
    :field="field"
    :rules="rules"
    :design-state="designState"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
    :sub-form-row-index="subFormRowIndex"
    :sub-form-col-index="subFormColIndex"
    :sub-form-row-id="subFormRowId"
    v-if="showField"
  >
    <el-slider
      ref="fieldEditor"
      v-model="fieldModel"
      :disabled="field.options.disabled"
      :show-stops="field.options.showStops"
      :min="field.options.min"
      :max="field.options.max"
      :step="field.options.step"
      :range="field.options.range"
      :vertical="field.options.vertical"
      @change="handleChangeEvent"
    >
    </el-slider>
  </form-item-wrapper>
</template>

<script>
import FormItemWrapper from './form-item-wrapper'
import emitter from '@/utils/emitter'
import i18n, { translate } from '@/utils/i18n'
import fieldMixin from '@/components/form-designer/form-widget/field-widget/fieldMixin'
import actionsHandlerMixin from './actionsHandlerMixin'
export default {
  name: 'slider-widget',
  componentName: 'FieldWidget', //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  mixins: [emitter, fieldMixin, i18n, actionsHandlerMixin],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,

    designState: {
      type: Boolean,
      default: false
    },

    subFormRowIndex: {
      /* 子表单组件行索引，从0开始计数 */ type: Number,
      default: -1
    },
    subFormColIndex: {
      /* 子表单组件列索引，从0开始计数 */ type: Number,
      default: -1
    },
    subFormRowId: {
      /* 子表单组件行Id，唯一id且不可变 */ type: String,
      default: ''
    }
  },
  components: {
    FormItemWrapper
  },
  inject: [
    'refList',
    'formConfig',
    'globalOptionData',
    'globalModel'
  ],
  data() {
    return {
      oldFieldValue: null, //field组件change之前的值
      rules: []
    }
  },

  created() {
    this.initFieldModel()
    this.registerToRefList()
    this.initEventHandler()
    this.buildFieldRules()

    this.handleOnCreated()
  },

  mounted() {
    this.handleOnMounted()
  },

  beforeDestroy() {
    this.unregisterFromRefList()
  },

  methods: {}
}
</script>

<style lang="scss" scoped>
@import '../../../../styles/global.scss';

.full-width-input {
  width: 100% !important;
}
</style>
