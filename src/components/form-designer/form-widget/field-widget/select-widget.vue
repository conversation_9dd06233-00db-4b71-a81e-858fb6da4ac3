<template>
  <form-item-wrapper
    :designer="designer"
    :field="field"
    :rules="rules"
    :design-state="designState"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
    :sub-form-row-index="subFormRowIndex"
    :sub-form-col-index="subFormColIndex"
    :sub-form-row-id="subFormRowId"
    v-if="showField"
  >
    <el-select
      ref="fieldEditor"
      v-model="fieldModel"
      class="full-width-select"
      :disabled="field.options.disabled"
      :size="field.options.size"
      :clearable="field.options.clearable"
      :filterable="field.options.filterable"
      :allow-create="field.options.allowCreate"
      :default-first-option="allowDefaultFirstOption"
      :automatic-dropdown="field.options.automaticDropdown"
      :multiple="field.options.multiple"
      :multiple-limit="field.options.multipleLimit"
      :placeholder="
        field.options.placeholder ||
        i18nt('render.hint.selectPlaceholder')
      "
      :popper-append-to-body="true"
      :popper-class="isMobile ? 'select-popper' : ''"
      :remote="field.options.remote"
      :remote-method="remoteMethod"
      @focus="handleFocusCustomEvent"
      @blur="handleBlurCustomEvent"
      @change="(value) => handleChangeEvent(value, false)"
    >
      <el-option
        v-for="item in optionItems"
        :key="item.value"
        :label="item.label"
        :value="item.value"
        :disabled="item.disabled"
      >
      </el-option>
    </el-select>
  </form-item-wrapper>
</template>

<script>
import FormItemWrapper from './form-item-wrapper'
import emitter from '@/utils/emitter'
import isMobile from '@/utils/isMobile'
import i18n from '@/utils/i18n'
import computedSettingMixins from './computedSettingMixins'
import fieldMixin from '@/components/form-designer/form-widget/field-widget/fieldMixin'
import actionsHandlerMixin from '@/components/form-designer/form-widget/field-widget/actionsHandlerMixin'
export default {
  name: 'select-widget',
  componentName: 'FieldWidget', // 必须固定为FieldWidget，用于接收父级组件的broadcast事件
  mixins: [
    isMobile,
    emitter,
    fieldMixin,
    i18n,
    actionsHandlerMixin,
    computedSettingMixins
  ],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,
    rootList: Array,
    designState: {
      type: Boolean,
      default: false
    },
    // containerName: String,

    subFormRowIndex: {
      /* 子表单组件行索引，从0开始计数 */ type: Number,
      default: -1
    },
    subFormColIndex: {
      /* 子表单组件列索引，从0开始计数 */ type: Number,
      default: -1
    },
    subFormRowId: {
      /* 子表单组件行Id，唯一id且不可变 */ type: String,
      default: ''
    }
  },
  components: {
    FormItemWrapper
  },
  inject: [
    'refList',
    'formConfig',
    'globalOptionData',
    'globalModel'
  ],
  data() {
    return {
      oldFieldValue: null, // field组件change之前的值
      rules: [],
      optionItems: []
    }
  },
  computed: {
    allowDefaultFirstOption() {
      return (
        !!this.field.options.filterable &&
        !!this.field.options.allowCreate
      )
    },
    authorization() {
      return localStorage.getItem('BearToken')
    },
    remoteMethod() {
      if (
        !!this.field.options.remote &&
        !!this.field.options.onRemoteQuery
      ) {
        return this.remoteQuery
      } else {
        return undefined
      }
    }
  },
  watch: {},
  created() {
    /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
       需要在父组件created中初始化！！ */
    this.initFieldModel()
    this.registerToRefList()
    this.initEventHandler()
    this.buildFieldRules()
    this.handleOnCreated()
    // 如果没有依赖父级 就直接初始化
    if (
      this.field.options.associatedParent === undefined ||
      !this.field.options.associatedParent.length
    ) {
      this.initSelectOptionItems(true)
    }
  },

  mounted() {
    this.handleOnMounted()
    if (this.isIos) {
      this.$refs.fieldEditor.$el.addEventListener(
        'pointerdown',
        (e) => {
          e.preventDefault()
          e.stopPropagation()
          setTimeout(() => {
            // this.$refs.fieldEditor.visible = true
            this.$refs.fieldEditor.handleFocus(e)
          }, 100)
        }
      )
    }
  },

  beforeDestroy() {
    this.unregisterFromRefList()
  },

  methods: {
    initFieldModelValue(isInit = false) {
      if (!isInit) {
        if (this.optionItems.length === 1) {
          const firstOption = this.optionItems[0]
          if (firstOption) {
            if (this.field.options.multiple) {
              this.fieldModel = [firstOption.value]
            } else {
              this.fieldModel = firstOption.value
            }
            this.handleChangeEvent(this.fieldModel, isInit)
          }
        } else {
          // 返回的下拉选有多条或者为空
          const fieldModelOption = this.optionItems.find(
            (item) => item.value == this.fieldModel
          )
          if (!fieldModelOption) {
            if (this.field.options.multiple) {
              this.fieldModel = []
            } else {
              this.fieldModel = null
            }
            this.handleChangeEvent(this.fieldModel, isInit)
          } else {
            this.handleChangeEvent(this.fieldModel, isInit)
          }
        }
      } else {
        if (this.optionItems.length === 1) {
          const firstOption = this.optionItems[0]
          if (firstOption) {
            if (this.field.options.multiple) {
              this.fieldModel = [firstOption.value]
            } else {
              this.fieldModel = firstOption.value
            }
            this.handleChangeEvent(this.fieldModel, isInit)
          }
        } else {
          this.handleChangeEvent(this.fieldModel, isInit)
        }
      }
    },
    initSelectOptionItems(isInit = false) {
      // if (this.isEnter) {
      if (
        this.field.options.optionSettingType == 'fixedValue'
      ) {
        this.getFixedValueItemOptions(isInit)
      } else if (
        this.field.options.optionSettingType ==
        'interfaceAddress'
      ) {
        this.getInterfaceAddressItemOptions(isInit)
      } else if (
        this.field.options.optionSettingType == 'dataMarket'
      ) {
        this.getDataMarketItemOptions(isInit)
      }
    },
    getRequestParams(requestParams = '') {
      if (requestParams == '') {
        return {}
      }
      const reg = /\$\{(\w+)\}/g
      requestParams = requestParams.replace(
        reg,
        (_match, p1) => {
          const value = this.formModel[p1]
          return value ? value : null
        }
      )
      return Object.fromEntries(
        new URLSearchParams(requestParams).entries()
      )
    },

    async getInterfaceAddressItemOptions(isInit = false) {
      // 请求地址
      const url =
        this.field.options.interfaceAddressRequestUrl
      if (!url) {
        console.warn('请配置请求地址')
        return
      }
      // 请求方式
      const method =
        this.field.options.interfaceAddressRequestMethod

      // 请求参数字符串
      const interfaceAddressRequestParams =
        this.field.options.interfaceAddressRequestParams

      // 请求参数
      const requestParams = this.getRequestParams(
        interfaceAddressRequestParams,
        isInit
      )

      let interfaceAddressResult = null
      if (method == 'get') {
        const requestUrl = `${url}${
          Object.keys(requestParams).length > 0
            ? '?' +
              new URLSearchParams(requestParams).toString()
            : ''
        }`
        interfaceAddressResult = await fetch(requestUrl, {
          method: 'get',
          headers: {
            Authorization: `Bearer ${this.authorization}`
          }
        }).then((res) => res.json())
      } else {
        interfaceAddressResult = await fetch(url, {
          method: 'post',
          body: JSON.stringify(requestParams),
          headers: {
            Authorization: `Bearer ${this.authorization}`,
            'Content-Type': 'application/json'
          }
        }).then((res) => res.json())
      }
      if (interfaceAddressResult.code == 200) {
        const options = interfaceAddressResult.data
        if (!options || !Array.isArray(options)) {
          this.$message.error(
            this.i18nt('designer.hint.dataNotAllow')
          )
          return
        }
        this.optionItems = options.map((item) => {
          // 接口地址展示label的key

          const interfaceAddressResponseLabelKey =
            this.field.options
              .interfaceAddressResponseLabelKey
          if (interfaceAddressResponseLabelKey) {
            item.label =
              item[interfaceAddressResponseLabelKey]
          }
          // 接口地址展示value的key

          const interfaceAddressResponseValueKey =
            this.field.options
              .interfaceAddressResponseValueKey
          if (interfaceAddressResponseValueKey) {
            item.value =
              item[interfaceAddressResponseValueKey]
          }
          return item
        })
        this.initFieldModelValue(isInit)
      } else {
        this.optionItems = []
        this.initFieldModelValue(isInit)
      }
    },
    /**
     * 获取数据市场的下拉选项
     * @param triggerType 触发方式
     */
    async getDataMarketItemOptions(isInit = false) {
      // 数据市场请求id
      const dataMarketRequestId =
        this.field.options.dataMarketRequestId
      if (!dataMarketRequestId) {
        return
      }
      const VUE_APP_BASE_API = process.env.VUE_APP_BASE_API
      // const VUE_APP_BASE_API = process.env.VUE_APP_BASE_API;
      const getRequestDetailUrl = `${VUE_APP_BASE_API}/data/market/dataApis/detail/${dataMarketRequestId}`
      const { apiHeader, apiUrl, reqMethod, apiVersion } =
        await fetch(getRequestDetailUrl, {
          method: 'get',
          headers: {
            Authorization: `Bearer ${this.authorization}`
          }
        })
          .then((res) => res.json())
          .then((result) => {
            const requestDetail = {
              apiVersion: '',
              apiHeader: {},
              apiUrl: '',
              reqMethod: ''
            }
            if (result.code == 200) {
              requestDetail.apiHeader = result.data.header
              requestDetail.apiVersion =
                result.data.data.apiVersion
              requestDetail.apiUrl = result.data.data.apiUrl
              requestDetail.reqMethod =
                result.data.data.reqMethod
            }
            return requestDetail
          })
      // 拼接数据市场的请求地址
      const baseRequestUrl = `${VUE_APP_BASE_API}/data/api/services/${apiVersion}${apiUrl}`
      // 用户输入的请求参数

      const dataMarketRequestParams =
        this.field.options.dataMarketRequestParams

      const requestParams = this.getRequestParams(
        dataMarketRequestParams,
        isInit
      )
      let dataMarketResult = null
      if (reqMethod.toLocaleLowerCase() == 'get') {
        const requestParamsStr =
          JSON.stringify(requestParams) === '{}'
            ? ''
            : `?${new URLSearchParams(
                requestParams
              ).toString()}`
        const requestUrl = `${baseRequestUrl}${requestParamsStr}`
        dataMarketResult = await fetch(requestUrl, {
          method: 'get',
          headers: {
            ...apiHeader,
            Authorization: `Bearer ${this.authorization}`
          }
        }).then((res) => res.json())
      } else {
        // post
        dataMarketResult = await fetch(baseRequestUrl, {
          method: 'post',
          body: JSON.stringify(requestParams),
          headers: {
            ...apiHeader,
            'Content-Type': 'application/json',
            Authorization: `Bearer ${this.authorization}`
          }
        }).then((res) => res.json())
      }
      if (dataMarketResult.code == 200) {
        const options = dataMarketResult.data
        if (!options || !Array.isArray(options)) {
          this.$message.error(
            this.i18nt('designer.hint.dataNotAllow')
          )
          return
        }
        this.optionItems = options.map((item) => {
          // 接口地址展示label的ke

          const dataMarketResponseLabelKey =
            this.field.options.dataMarketResponseLabelKey

          if (dataMarketResponseLabelKey) {
            item.label = item[dataMarketResponseLabelKey]
          }
          // 接口地址展示value的key

          const dataMarketResponseValueKey =
            this.field.options.dataMarketResponseValueKey

          if (dataMarketResponseValueKey) {
            item.value = item[dataMarketResponseValueKey]
          }
          return item
        })
        this.initFieldModelValue(isInit)
      } else {
        this.optionItems = []
        this.initFieldModelValue(isInit)
      }
    },
    getFixedValueItemOptions(isInit = false) {
      this.optionItems = this.field.options.optionItems
      this.initFieldModelValue(isInit)
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../../../../styles/global.scss'; //* form-item-wrapper已引入，还需要重复引入吗？ *//

.full-width-select {
  width: 100% !important;
}
</style>
<style lang="scss">
.el-scrollbar > .el-scrollbar__bar {
  opacity: 1 !important;
}

.select-popper {
  .el-select-dropdown__list {
    padding-bottom: 20px !important;
  }
}
</style>
