<template>
  <form-item-wrapper
    :designer="designer"
    :field="field"
    :rules="rules"
    :design-state="designState"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
    :sub-form-row-index="subFormRowIndex"
    :sub-form-col-index="subFormColIndex"
    :sub-form-row-id="subFormRowId"
    v-if="showField"
  >
    <div
      style="
        display: flex;
        align-items: center;
        width: 100%;
      "
    >
      <el-input
        class="full-width-location"
        v-model="fieldModel"
        ref="fieldEditor"
        type="textarea"
        :autosize="{ minRows: 2, maxRows: 4 }"
        :disabled="field.options.disabled"
        :readonly="field.options.readonly"
        :size="field.options.size"
        :placeholder="field.options.placeholder"
        :clearable="field.options.clearable"
        @focus="handleFocusCustomEvent"
        @blur="handleBlurCustomEvent"
        @change="handleChangeEvent"
        @input="handleInputCustomEvent"
        style="flex: 1"
        clearable
      />
      <el-button
        icon="el-icon-location-outline"
        @click="getLocation"
        style="margin-left: 8px; height: 40px"
        type="primary"
      ></el-button>
    </div>
    <el-dialog
      append-to-body
      :visible.sync="dialogVisible"
      title="地址选择"
      width="50%"
    >
      <div
        @click="selectRow(idx)"
        v-for="(item, idx) in addressOptions"
        :key="idx"
        :class="[
          'location-option-item',
          { active: selectedIdx === idx }
        ]"
        @click.stop
      >
        <div>{{ item.lnglat }}</div>
        <div v-if="item.address">{{ item.address }}</div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm"
          >确定</el-button
        >
      </span>
    </el-dialog>
  </form-item-wrapper>
</template>

<script>
import FormItemWrapper from './form-item-wrapper'
import emitter from '@/utils/emitter'
import isMobile from '@/utils/isMobile'
import i18n from '@/utils/i18n'
import fieldMixin from '@/components/form-designer/form-widget/field-widget/fieldMixin'
import actionsHandlerMixin from './actionsHandlerMixin'
export default {
  componentName: 'FieldWidget', // 必须固定为FieldWidget，用于接收父级组件的broadcast事件
  mixins: [
    isMobile,
    emitter,
    fieldMixin,
    i18n,
    actionsHandlerMixin
  ],
  name: 'location-widget',
  components: {
    FormItemWrapper
  },
  data() {
    return {
      geolocation: null,
      geocoder: null,
      addressOptions: [],
      dialogVisible: false,
      selectedIdx: null,
      selectedAddress: null,
      rules: []
    }
  },
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,
    rootList: Array,
    designState: {
      type: Boolean,
      default: false
    },
    // containerName: String,

    subFormRowIndex: {
      /* 子表单组件行索引，从0开始计数 */ type: Number,
      default: -1
    },
    subFormColIndex: {
      /* 子表单组件列索引，从0开始计数 */ type: Number,
      default: -1
    },
    subFormRowId: {
      /* 子表单组件行Id，唯一id且不可变 */ type: String,
      default: ''
    }
  },
  methods: {
    showError(message) {
      this.$message.error(message)
    },
    getLocation() {
      if (!window.AMap) {
        this.showError('地图SDK加载失败，请检查网络连接')
        return
      }

      AMap.plugin('AMap.Geolocation', () => {
        this.geolocation = new AMap.Geolocation({
          enableHighAccuracy: true,
          timeout: 10000,
          position: 'RB',
          offset: [10, 20],
          zoomToAccuracy: true
        })
        this.geolocation.getCurrentPosition(
          (status, result) => {
            if (status === 'complete') {
              const lng = result.position.getLng()
              const lat = result.position.getLat()
              const locationVariations =
                this.generateLocationVariations(lng, lat)
              this.getAddresses(
                locationVariations.map((item) => [
                  item.lng,
                  item.lat
                ])
              )
            } else {
              let errorMsg = '定位失败'
              if (status === 'error') {
                switch (result.message) {
                  case 'PERMISSION_DENIED':
                    errorMsg =
                      '定位失败：用户拒绝了定位请求'
                    break
                  case 'POSITION_UNAVAILABLE':
                    errorMsg = '定位失败：位置信息不可用'
                    break
                  case 'TIMEOUT':
                    errorMsg = '定位失败：请求超时'
                    break
                  default:
                    errorMsg = `定位失败：${result.message}`
                }
              }
              this.showError(errorMsg)
              this.fieldModel = ''
              this.addressOptions = []
            }
          }
        )
      })
    },
    generateLocationVariations(lng, lat) {
      const OFFSET = 0.01 // 经纬度偏移量

      // 生成5个位置变体：
      // 1. 原始位置
      // 2. 经度向东偏移
      // 3. 经度向西偏移
      // 4. 纬度向北偏移
      // 5. 纬度向南偏移
      return [
        { lng, lat, description: '当前位置' },
        { lng: lng + OFFSET, lat, description: '向东偏移' },
        { lng: lng - OFFSET, lat, description: '向西偏移' },
        { lng, lat: lat + OFFSET, description: '向北偏移' },
        { lng, lat: lat - OFFSET, description: '向南偏移' }
      ]
    },
    getAddresses(lnglats) {
      let city = ''
      AMap.plugin('AMap.CitySearch', function () {
        const citySearch = new AMap.CitySearch()
        citySearch.getLocalCity(function (status, result) {
          if (
            status === 'complete' &&
            result.info === 'OK'
          ) {
            city = result.city
          }
        })
      })
      AMap.plugin('AMap.Geocoder', () => {
        this.geocoder = new AMap.Geocoder({
          city: city
        })
        this.geocoder.getAddress(
          lnglats,
          (status, result) => {
            if (
              status === 'complete' &&
              result.info === 'OK'
            ) {
              this.addressOptions = result.regeocodes.map(
                (item) => ({
                  lnglat: item.location,
                  address: item.formattedAddress
                })
              )
              this.dialogVisible = true
            } else {
              let errorMsg = '地址解析失败'
              if (
                result.info === 'DAILY_QUERY_OVER_LIMIT'
              ) {
                errorMsg = 'API调用次数已达上限，请明天再试'
              } else if (result.info === 'INVALID_KEY') {
                errorMsg = 'API密钥无效，请联系管理员'
              } else if (
                result.info === 'IP_QUERY_OVER_LIMIT'
              ) {
                errorMsg = 'IP请求次数超限，请稍后再试'
              }
              this.showError(errorMsg)
            }
          }
        )
      })
    },
    selectRow(idx) {
      this.selectedIdx = idx
    },
    handleConfirm() {
      if (this.selectedIdx !== null) {
        this.selectedAddress =
          this.addressOptions[this.selectedIdx]
        this.fieldModel = this.selectedAddress.address
      }
      this.dialogVisible = false
    },
    handleCancel() {
      this.dialogVisible = false
    }
  },
  created() {
    /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
           需要在父组件created中初始化！！ */
    this.initFieldModel()
    this.registerToRefList()
    this.initEventHandler()
    this.buildFieldRules()
    this.handleOnCreated()
  },
  beforeCreate() {}
}
</script>

<style lang="scss" scoped>
@import '../../../../styles/global.scss'; //* form-item-wrapper已引入，还需要重复引入吗？ *//

.full-width-location {
  width: 100% !important;
}
</style>
<style lang="scss">
.location-option-item {
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
  color: #333;
  font-size: 16px;
  cursor: pointer;
}

.location-option-item:last-child {
  border-bottom: none;
}

.location-option-item.active {
  background: #f0f9eb;
  border: 1px solid #67c23a;
}
</style>
