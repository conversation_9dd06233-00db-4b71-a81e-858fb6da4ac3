<template>
  <form-item-wrapper
    :designer="designer"
    :field="field"
    :rules="rules"
    :design-state="designState"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
    :sub-form-row-index="subFormRowIndex"
    :sub-form-col-index="subFormColIndex"
    :sub-form-row-id="subFormRowId"
    v-if="showField"
  >
    <div class="table-header" style="margin-bottom: 5px">
      <el-button
        size="mini"
        @click="handleAddTableData"
        type="success"
        >新建</el-button
      >
    </div>
    <el-table
      :data="fieldModel"
      border
      stripe
      tooltip-effect="light"
      style="width: 100%"
    >
      <template v-for="item in columns">
        <el-table-column
          :key="item.key"
          :prop="item.prop"
          :label="item.label"
          :min-width="item.width"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
      </template>
      <el-table-column label="操作" width="200">
        <template slot-scope="scope">
          <el-button
            type="primary"
            @click="
              handleEditTableData(scope.row, scope.$index)
            "
            size="mini"
            >编辑</el-button
          >
          <el-button
            type="danger"
            @click="handleDeleteTable(scope.$index)"
            size="mini"
            >删除</el-button
          >
        </template>
      </el-table-column>
      <!-- TODO -->
    </el-table>
    <div
      class="table-footer"
      style="margin-top: 5px"
      v-if="!previewState"
    >
      <el-button size="mini" @click="handleDesignTable"
        >设计表格</el-button
      >
    </div>
    <!-- 设计表格 -->
    <el-dialog
      v-dialog-drag
      append-to-body
      :visible.sync="designTableDialogVisible"
      title="设计表格"
      width="90%"
    >
      <el-steps
        :active="activeStep"
        style="margin-bottom: 10px"
      >
        <el-step title="表单基础信息"></el-step>
        <el-step title="表单设计"></el-step>
      </el-steps>
      <el-form
        v-if="activeStep === 1"
        ref="formInfoRef"
        :rules="formInfoRules"
        :model="formInfo"
        label-width="auto"
      >
        <el-form-item label="分类" prop="classify">
          <el-select
            v-model="formInfo.classify"
            placeholder="分类"
            class="filter-item"
            filterable
            clearable
          >
            <el-option
              v-for="item in categoryList"
              :key="item.dictLabel"
              :label="item.dictLabel"
              :value="item.dictLabel"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="表名" prop="tableName">
          <el-input
            v-model="formInfo.tableName"
            placeholder="请输入表名"
          />
        </el-form-item>
        <el-form-item label="表编码" prop="tableCode">
          <el-input
            v-model="formInfo.tableCode"
            placeholder="请输入表编码"
          />
        </el-form-item>
      </el-form>
      <VFormDesigner
        v-if="activeStep === 2"
        style="height: 500px"
        ref="vfTableDesignerRef"
        :designer-config="designerConfig"
        :parent-container-name="'table'"
      >
      </VFormDesigner>
      <span slot="footer" class="dialog-footer">
        <el-button @click="designTableDialogVisible = false"
          >取 消</el-button
        >
        <!-- 上一步 -->
        <el-button
          v-if="activeStep === 2"
          type="primary"
          @click="activeStep = 1"
          >上一步</el-button
        >
        <!-- 下一步 -->
        <el-button
          v-if="activeStep === 1"
          type="primary"
          @click="handleNextStep"
          >下一步</el-button
        >
        <el-button
          v-if="activeStep === 2"
          type="primary"
          @click="handleDesignTableConfirm"
          >建表</el-button
        >
      </span>
    </el-dialog>

    <!-- 预览dialog -->
    <el-dialog
      :title="previewDialogTitle"
      :visible.sync="showPreviewDialogFlag"
      :show-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      center
      v-dialog-drag
      :destroy-on-close="true"
      :append-to-body="true"
      class="small-padding-dialog"
      width="75%"
    >
      <!-- <div v-if="showPreviewDialogFlag" class="form-render-wrapper" :class="[
        layoutType === 'H5'
          ? 'h5-layout'
          : layoutType === 'Pad'
            ? 'pad-layout'
            : ''
      ]"> -->
      <VFormRender
        v-if="showPreviewDialogFlag"
        :handle-type="handleType"
        ref="tablePreForm"
        :form-json="formJson"
        :form-data="formData"
        :preview-state="true"
      >
      </VFormRender>
      <!-- </div> -->
      <div slot="footer" class="dialog-footer">
        <el-button
          v-if="!previewState"
          type="primary"
          @click="getFormData"
          >{{
            i18nt('designer.hint.getFormData')
          }}</el-button
        >
        <el-button
          type="primary"
          @click="setFormData2Json"
          >{{
            i18nt('designer.hint.setFormData2Json')
          }}</el-button
        >
        <el-button
          v-if="!previewState"
          type="primary"
          @click="vaildForm"
          >表单验证</el-button
        >
        <el-button
          v-if="!previewState"
          type="primary"
          @click="resetForm"
          >{{ i18nt('designer.hint.resetForm') }}</el-button
        >
        <el-button
          v-if="!previewState"
          type="primary"
          @click="setFormDisabled"
          >{{
            i18nt('designer.hint.disableForm')
          }}</el-button
        >
        <el-button
          v-if="!previewState"
          type="primary"
          @click="setFormEnabled"
          >{{
            i18nt('designer.hint.enableForm')
          }}</el-button
        >
        <el-button
          v-if="!previewState"
          type=""
          @click="showPreviewDialogFlag = false"
          >{{
            i18nt('designer.hint.closePreview')
          }}</el-button
        >
      </div>
    </el-dialog>
    <!-- 查看数据 -->
    <el-dialog
      :title="i18nt('designer.hint.exportFormData')"
      :visible.sync="showFormDataDialogFlag"
      v-if="showFormDataDialogFlag"
      :show-close="true"
      class="dialog-title-light-bg"
      center
      v-dialog-drag
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
      :append-to-body="true"
    >
      <div style="border: 1px solid #dcdfe6">
        <code-editor
          :mode="'json'"
          :readonly="true"
          v-model="formDataJson"
        ></code-editor>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          class="copy-form-data-json-btn"
          :data-clipboard-text="formDataRawJson"
          @click="copyFormDataJson"
        >
          {{
            i18nt('designer.hint.copyFormData')
          }}</el-button
        >
        <el-button @click="saveFormData">{{
          i18nt('designer.hint.saveFormData')
        }}</el-button>
        <el-button
          type=""
          @click="showFormDataDialogFlag = false"
        >
          {{
            i18nt('designer.hint.closePreview')
          }}</el-button
        >
      </div>
    </el-dialog>
  </form-item-wrapper>
</template>

<script>
import i18n from '@/utils/i18n'
import refMixinDesign from '@/components/form-designer/refMixinDesign'
import actionsHandlerMixin from './actionsHandlerMixin'
import FormItemWrapper from './form-item-wrapper'
import CodeEditor from '@/components/code-editor/index'

import fieldMixin from '@/components/form-designer/form-widget/field-widget/fieldMixin'
const VFormDesigner = () =>
  import('@/components/form-designer/index.vue')
const VFormRender = () =>
  import('@/components/form-render/index.vue')
export default {
  name: 'table-field-widget',
  componentName: 'FieldWidget',
  mixins: [
    i18n,
    fieldMixin,
    refMixinDesign,
    actionsHandlerMixin
  ],
  inject: ['refList', 'handleType', 'previewState'],
  components: {
    VFormDesigner,
    FormItemWrapper,
    VFormRender,
    CodeEditor
  },
  props: {
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,
    field: Object,
    designState: {
      type: Boolean,
      default: false
    },

    subFormRowIndex: {
      /* 子表单组件行索引，从0开始计数 */ type: Number,
      default: -1
    },
    subFormColIndex: {
      /* 子表单组件列索引，从0开始计数 */ type: Number,
      default: -1
    },
    subFormRowId: {
      /* 子表单组件行Id，唯一id且不可变 */ type: String,
      default: ''
    }
  },
  computed: {
    authorization() {
      return localStorage.getItem('BearToken')
    },
    selected() {
      return this.field.id === this.designer.selectedId
    },
    customClass() {
      return this.field.options.customClass || ''
    }
    // layoutType() {
    //   return this.designer.getLayoutType()
    // },
  },
  data() {
    return {
      previewDialogTitle: '',
      showPreviewDialogFlag: false,
      formDataRawJson: '',
      showFormDataDialogFlag: false,
      activeStep: 1,
      designerConfig: {
        resetFormJson: false,
        toolbarMaxWidth: 490
      },
      designTableDialogVisible: false,
      rules: [],
      originTableCode: null,
      formInfo: {
        classify: '',
        tableName: '',
        tableCode: ''
      },
      categoryList: [],
      formInfoRules: {
        classify: [
          {
            required: true,
            message: '请选择分类',
            trigger: 'change'
          }
        ],
        tableName: [
          {
            required: true,
            message: '请输入表名',
            trigger: 'blur'
          }
        ],
        tableCode: [
          {
            required: true,
            message: '请输入表编码',
            trigger: 'blur'
          }
        ]
      },
      formData: {},
      formJson: {
        widgetList: [],
        formConfig: {},
        searchConfigList: []
      },
      editRowIndex: -1,
      editRowType: 'add',
      columns: []
    }
  },
  created() {
    this.initRefList()
    this.inItColumns()
  },
  methods: {
    inItColumns() {
      const tableCode = this.field.options.tableCode
      if (!tableCode) {
        this.columns = []
      } else {
        let childrenConfigMap = localStorage.getItem(
          'children_config_map'
        )
        childrenConfigMap = childrenConfigMap
          ? JSON.parse(childrenConfigMap)
          : {}
        const childrenConfig = childrenConfigMap[tableCode]
        if (!childrenConfig) {
          this.columns = []
        } else {
          this.columns = childrenConfig.widgetList.map(
            (item) => {
              return {
                key: item.id,
                prop: item.options.name,
                label: item.options.label,
                width: item.options.columnWidth
              }
            }
          )
        }
      }
    },
    handleEditTableData(row, index) {
      if (!!this.previewState) {
        this.previewDialogTitle = '编辑'
      } else {
        this.previewDialogTitle = '设计编辑'
      }
      this.editRowType = 'edit'
      this.formData = row
      let childrenConfigMap = localStorage.getItem(
        'children_config_map'
      )
      childrenConfigMap = childrenConfigMap
        ? JSON.parse(childrenConfigMap)
        : {}
      const childrenConfig =
        childrenConfigMap[this.field.options.tableCode]
      this.formJson = childrenConfig
      this.editRowIndex = index
      this.showPreviewDialogFlag = true
    },
    handleDeleteTable(index) {
      this.fieldModel.splice(index, 1)
    },
    handleAddTableData() {
      if (!!this.previewState) {
        this.previewDialogTitle = '新建'
      } else {
        this.previewDialogTitle = '设计新建'
      }
      this.editRowType = 'add'
      this.formData = {}
      let childrenConfigMap = localStorage.getItem(
        'children_config_map'
      )
      childrenConfigMap = childrenConfigMap
        ? JSON.parse(childrenConfigMap)
        : {}
      const childrenConfig =
        childrenConfigMap[this.field.options.tableCode]
      this.formJson = childrenConfig
      this.showPreviewDialogFlag = true
    },
    getFormData() {
      this.$refs['tablePreForm']
        .getFormData()
        .then((formData) => {
          this.formDataRawJson = JSON.stringify(formData)
          this.showFormDataDialogFlag = true
        })
        .catch((error) => {
          this.$message.error(error)
        })
    },
    setFormData2Json() {
      this.$refs['tablePreForm']
        .getFormData()
        .then((formData) => {
          if (this.editRowType === 'add') {
            this.fieldModel.push(formData)
          } else {
            this.fieldModel[this.editRowIndex] = formData
            this.fieldModel = JSON.parse(
              JSON.stringify(this.fieldModel)
            )
          }
          this.showPreviewDialogFlag = false
        })
        .catch((error) => {
          this.$message.error(error)
        })
    },
    vaildForm() {
      this.$refs['tablePreForm'].validateForm()
    },
    resetForm() {
      this.$refs['tablePreForm'].resetForm()
    },
    setFormDisabled() {
      this.$refs['tablePreForm'].disableForm()
    },
    setFormEnabled() {
      this.$refs['tablePreForm'].enableForm()
    },
    /**
     * 设计表单
     */
    async handleDesignTable() {
      this.activeStep = 2
      await this.handleGetCategoryList()
      this.designTableDialogVisible = true
      this.$nextTick(() => {
        this.$refs.formInfoRef.resetFields()
        this.formInfo.classify = this.field.options.classify
        this.formInfo.tableName =
          this.field.options.tableName
        this.formInfo.tableCode =
          this.field.options.tableCode
      })
    },
    /**
     * 下一步
     */
    async handleNextStep() {
      const valid = await this.$refs.formInfoRef
        .validate()
        .catch(() => false)
      if (!valid) return
      // 将数据存储到配置中
      this.field.options.classify = this.formInfo.classify
      this.field.options.tableName = this.formInfo.tableName
      this.field.options.tableCode = this.formInfo.tableCode
      // 拉取表单设计数据
      // 从接口中取数据存放到缓存中再取数据
      let childrenConfigMap = localStorage.getItem(
        'children_config_map'
      )
      childrenConfigMap = childrenConfigMap
        ? JSON.parse(childrenConfigMap)
        : {}
      if (!childrenConfigMap) {
        const VUE_APP_BASE_API =
          process.env.VUE_APP_BASE_API
        const res = await fetch(
          `${VUE_APP_BASE_API}/codeDev/formDesign/list?tableName=${this.formInfo.tableName}`,
          {
            method: 'get',
            headers: {
              Authorization: `Bearer ${this.authorization}`
            }
          }
        )
        if (res.code == 200) {
          const row = res.rows
          if (row.length > 0) {
            let currentFormJsonInfo = row[0].formJsonInfo
            currentFormJsonInfo = JSON.parse(
              currentFormJsonInfo
            )
            childrenConfigMap[tableCode] =
              currentFormJsonInfo
          } else {
            childrenConfigMap = {}
          }
        } else {
          this.$message.error(res.msg)
        }
      }
      const currentChildrenConfig =
        childrenConfigMap[this.formInfo.tableCode] || {}
      const widgetList =
        currentChildrenConfig.widgetList || []
      const formConfig =
        currentChildrenConfig.formConfig || {}
      const searchConfigList =
        currentChildrenConfig.searchConfigList || []
      this.formJson = {
        widgetList,
        formConfig,
        searchConfigList
      }
      // window.localStorage.setItem(
      //   'table__widget__list__backup',
      //   JSON.stringify(widgetList)
      // )
      // window.localStorage.setItem(
      //   'table__form__config__backup',
      //   JSON.stringify(formConfig)
      // )
      // window.localStorage.setItem(
      //   'table__search__config__list__backup',
      //   JSON.stringify(searchConfigList)
      // )
      this.activeStep = 2
    },
    /**
     * 建表
     */
    handleDesignTableConfirm() {
      // 将数据存放到本地存储中
      const formJson =
        this.$refs.vfTableDesignerRef.getFormJson()
      let childrenConfigMap = localStorage.getItem(
        'children_config_map'
      )
      childrenConfigMap = childrenConfigMap
        ? JSON.parse(childrenConfigMap)
        : {}
      childrenConfigMap[this.formInfo.tableCode] = formJson
      localStorage.setItem(
        'children_config_map',
        JSON.stringify(childrenConfigMap)
      )
      this.inItColumns()
      this.$message.success('建表成功')
      this.designTableDialogVisible = false
    },

    /**
     * 获取分类列表
     */
    async handleGetCategoryList() {
      const VUE_APP_BASE_API = process.env.VUE_APP_BASE_API
      const res = await fetch(
        VUE_APP_BASE_API +
          '/system/dict/data/type/form_category',
        {
          method: 'get',
          headers: {
            Authorization: `Bearer ${this.authorization}`
          }
        }
      )
      const data = await res.json()
      // TODO 绑定字段
      console.log('data', data)
      if (data.code === 200) {
        this.categoryList = data.data
      }
    }
  }
}
</script>

<style lang="scss" scoped></style>
