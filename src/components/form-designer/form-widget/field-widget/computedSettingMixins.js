export default {
  methods: {
    // 收集依赖
    getDependencies() {
      // 过滤出可以作为依赖的组件列表
      const dependencyList = this.parentList.filter(
        (widget) => {
          const isNotContainer =
            widget.category !== 'container'
          const isNotSelf = widget.id !== this.field.id
          return isNotContainer && isNotSelf
        }
      )
      return dependencyList.map((item) => ({
        label: item.options.label,
        value: item.options.name
      }))
    },
    // 触发依赖
    triggerDependencie() {
      try {
        const computedSettingModel =
          this.field.options.computedSettingModel
        switch (computedSettingModel) {
          case 'default':
            return
          case 'simple':
            this.simpleComputedSetting()
            break
          case 'code':
            this.codeComputedSetting()
            break
          default:
            break
        }
      } catch (error) {
        console.log(error)
      }
    },
    /**
     * 简易模式
     */
    simpleComputedSetting() {
      try {
        const computedSetting =
          this.field.options.computedSetting
        if (!computedSetting) return

        const dependencies = this.getDependencies()
        let allowImplement = true
        const reg = /\$\{(\w+)\}/g //${?}
        const evalScript = computedSetting.replace(
          reg,
          (match, p1) => {
            const value = this.formModel[p1]
            if (!value) {
              allowImplement = false
            }
            const allowDependencies = dependencies.find(
              (item) => item.value === p1
            )
            if (!allowDependencies) {
              allowImplement = false
              this.$message.error(`${p1} 不能作为依赖`)
            }
            return value || match // 用对应的值替换 ${变量名}，找不到时保留原样
          }
        )
        if (allowImplement) {
          const evalValue = eval(evalScript)
          this.fieldModel = evalValue
        }
      } catch (error) {
        console.log('simpleComputedSetting error', error)
      }
    },
    /**
     * 代码模式
     */
    codeComputedSetting() {
      try {
        const computedSettingCode =
          this.field.options.computedSettingCode
        if (!computedSettingCode) return
        const computedSettingCodeFunction = new Function(
          computedSettingCode
        )
        const computedSettingCodeFunctionResult =
          computedSettingCodeFunction.call(this)
        this.fieldModel = computedSettingCodeFunctionResult
      } catch (error) {
        console.log('codeComputedSetting error', error)
      }
    }
  },
  created() {
    // 按需监听
    if (
      this.field.options.computedSettingModel !== 'default'
    ) {
      this.$watch(
        () => this.formModel,
        () => {
          this.triggerDependencie()
        },
        {
          deep: true,
          immediate: true // 为了防止有些情况 接口返回速度比vue的渲染快的情况 监听不到的情况
        }
      )
    }
  }
}
