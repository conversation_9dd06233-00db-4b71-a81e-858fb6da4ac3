export default {
  inject: ['globalModel', 'previewState'],
  props: {
    field: Object
  },
  data() {
    return {
      showField: true
    }
  },
  watch: {
    globalModel: {
      handler() {
        if (!this.previewState) return
        // 在设计阶段不触发逻辑执行
        this.doAction()
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    doAction() {
      const actions = this.field.options.actions
      if (actions && actions.length > 0) {
        // 有条件，执行条件
        actions.forEach((action, index) => {
          const { condition, type } = action
          if (type === 'show') {
            this.showField = this.executeCondition(
              condition,
              index
            )
          } else if (type === 'hide') {
            this.showField = !this.executeCondition(
              condition,
              index
            )
          } else {
            this.$message.error(`未知的动作类型 ${type}`)
          }
        })
      } else {
        // 没有条件，直接显示
        this.showField = true
      }
    },
    /**
     * 解析条件
     * @param {String} condition 条件
     * @returns {String} 解析后的条件
     */
    parseCondition(condition) {
      // 将 ${a} === 1 && ${b} === 2 转成 globalModel.a === 1 && globalModel.b === 2
      // 或者 ${a} === 1 || ${b} === 2 转成 globalModel.a === 1 || globalModel.b === 2
      const parsedCondition = condition.replace(
        /\$\{([^}]+)\}/g,
        (match, p1, offset) => {
          // 计算当前是第几个字段
          const fieldIndex =
            condition
              .substring(0, offset)
              .match(/\$\{([^}]+)\}/g)?.length || 0
          const fieldNumber = fieldIndex + 1
          // 如果匹配不到，则给用户提示 只匹配对象里面的属性  不包含原型
          if (!Object.hasOwn(this.globalModel, p1)) {
            this.$message.error(
              `${this.field.options.label} 组件条件中第 ${fieldNumber} 个字段 ${p1} 不合法`
            )
            throw new Error(
              `${this.field.options.label} 组件条件中第 ${fieldNumber} 个字段 ${p1} 不合法`
            )
          }
          return `this.globalModel.${p1}`
        }
      )
      return parsedCondition
    },
    /**
     * 执行条件
     * @param {String} condition 条件
     * @returns {Boolean} 执行结果
     */
    executeCondition(condition, index) {
      const parsedCondition = this.parseCondition(
        condition,
        index
      )
      return eval(parsedCondition)
    }
  }
}
