<template>
  <div class="form-widget-container">
    <el-form
      class="full-height-width widget-form"
      :label-position="labelPosition"
      :class="[customClass, layoutType + '-layout']"
      :size="size"
      :validate-on-rule-change="false"
    >
      <div
        v-if="designer.widgetList.length === 0"
        class="no-widget-hint"
      >
        {{ i18nt('designer.noWidgetHint') }}
      </div>
      <draggable
        :list="designer.widgetList"
        v-bind="{
          group: 'dragGroup',
          ghostClass: 'ghost',
          animation: 300
        }"
        handle=".drag-handler"
        @end="onDragEnd"
        @add="onDragAdd"
        @update="onDragUpdate"
        :move="checkMove"
        tag="div"
        class="form-widget-list"
      >
        <template
          v-for="(widget, index) in designer.widgetList"
        >
          <template v-if="'container' === widget.category">
            <!-- container-name="designer" -->
            <component
              :is="getWidgetName(widget)"
              :root-list="designer.widgetList"
              :widget="widget"
              :designer="designer"
              :key="widget.id"
              :parent-list="designer.widgetList"
              :index-of-parent-list="index"
              :parent-widget="null"
            ></component>
          </template>
          <template v-else>
            <!-- container-name="designer" -->
            <component
              :is="getWidgetName(widget)"
              :root-list="designer.widgetList"
              :field="widget"
              :designer="designer"
              :key="widget.id"
              :parent-list="designer.widgetList"
              :index-of-parent-list="index"
              :parent-widget="null"
              :design-state="true"
            ></component>
          </template>
        </template>
      </draggable>
    </el-form>
  </div>
</template>

<script>
import Draggable from 'vuedraggable'
// 注册全局组件
import '@/components/form-designer/form-widget/container-widget/index'
import FieldComponents from '@/components/form-designer/form-widget/field-widget/index'
import i18n from '@/utils/i18n'
import dayjs from 'dayjs'
import { deepClone } from '@/utils/util'
export default {
  name: 'VFormWidget',
  componentName: 'VFormWidget',
  mixins: [i18n],
  components: {
    Draggable,
    ...FieldComponents
  },
  props: {
    designer: Object,
    formConfig: Object,
    optionData: {
      // prop传入的选项数据
      type: Object,
      default: () => ({})
    },
    globalDsv: {
      type: Object,
      default: () => ({})
    }
  },
  provide() {
    return {
      formConfig: this.formConfig,
      getGlobalDsv: () => this.globalDsv, // 全局数据源变量
      globalOptionData: this.optionData,
      getOptionData: () => this.optionData,
      globalModel: this.formModel
    }
  },
  inject: ['getDesignerConfig', 'refList'],
  data() {
    return {
      formModel: {}
    }
  },
  computed: {
    labelPosition() {
      if (
        !!this.designer.formConfig &&
        !!this.designer.formConfig.labelPosition
      ) {
        return this.designer.formConfig.labelPosition
      }
      return 'left'
    },

    size() {
      if (
        !!this.designer.formConfig &&
        !!this.designer.formConfig.size
      ) {
        return this.designer.formConfig.size
      }

      return 'medium'
    },

    customClass() {
      return this.designer.formConfig.customClass || ''
    },

    layoutType() {
      return this.designer.getLayoutType()
    }
  },
  created() {
    this.designer.initDesigner(
      !!this.getDesignerConfig().resetFormJson
    )
    this.designer.loadPresetCssCode(
      this.getDesignerConfig().presetCssCode
    )
    this.buildFormModel(this.designer.widgetList)
  },
  mounted() {
    this.disableFirefoxDefaultDrop() /* 禁用Firefox默认拖拽搜索功能!! */
    this.designer.registerFormWidget(this)
  },
  methods: {
    buildFormModel(widgetList) {
      if (!!widgetList && widgetList.length > 0) {
        widgetList.forEach((wItem) => {
          this.buildDataFromWidget(wItem)
        })
      }
    },

    buildDataFromWidget(wItem) {
      if (wItem.category === 'container') {
        if (wItem.type === 'grid') {
          if (!!wItem.cols && wItem.cols.length > 0) {
            wItem.cols.forEach((childItem) => {
              this.buildDataFromWidget(childItem)
            })
          }
        } else if (wItem.type === 'table') {
          if (!!wItem.rows && wItem.rows.length > 0) {
            wItem.rows.forEach((rowItem) => {
              if (
                !!rowItem.cols &&
                rowItem.cols.length > 0
              ) {
                rowItem.cols.forEach((colItem) => {
                  this.buildDataFromWidget(colItem)
                })
              }
            })
          }
        } else if (wItem.type === 'tab') {
          if (!!wItem.tabs && wItem.tabs.length > 0) {
            wItem.tabs.forEach((tabItem) => {
              if (
                !!tabItem.widgetList &&
                tabItem.widgetList.length > 0
              ) {
                tabItem.widgetList.forEach((childItem) => {
                  this.buildDataFromWidget(childItem)
                })
              }
            })
          }
        } else if (wItem.type === 'sub-form') {
          let subFormDataRow = {}
          if (wItem.options.showBlankRow) {
            wItem.widgetList.forEach((subFormItem) => {
              if (!!subFormItem.formItemFlag) {
                subFormDataRow[subFormItem.options.name] =
                  subFormItem.options.defaultValue
              }
            })
            this.$set(this.formModel, subFormName, [
              subFormDataRow
            ])
          } else {
            this.$set(this.formModel, subFormName, [])
          }
        } else if (
          wItem.type === 'grid-col' ||
          wItem.type === 'table-cell'
        ) {
          if (
            !!wItem.widgetList &&
            wItem.widgetList.length > 0
          ) {
            wItem.widgetList.forEach((childItem) => {
              this.buildDataFromWidget(childItem)
            })
          }
        } else {
          //自定义容器组件
          if (
            !!wItem.widgetList &&
            wItem.widgetList.length > 0
          ) {
            wItem.widgetList.forEach((childItem) => {
              this.buildDataFromWidget(childItem)
            })
          }
        }
      } else if (!!wItem.formItemFlag) {
        if (wItem.type === 'date') {
          let initialValue = wItem.options.defaultValue
          const defaultValueSettingtTabName =
            wItem.options.defaultValueSettingtTabName
          if (
            defaultValueSettingtTabName === 'fixedValue'
          ) {
            initialValue = wItem.options.defaultValue
          } else if (
            defaultValueSettingtTabName === 'dynamicValue'
          ) {
            const dynamicValue = wItem.options.dynamicValue // 0代表今天 1 是明天 -1 是昨天 依次类推
            const valueFormat =
              wItem.options.valueFormat || ''
            const formatValue = valueFormat.replace(
              /\byyyy-MM-dd\b|\byyyy-MM-dd HH:mm:ss\b/g,
              (match) =>
                match.replace('yyyy-MM-dd', 'YYYY-MM-DD')
            )
            initialValue = dayjs()
              .add(dynamicValue, 'day')
              .format(formatValue)
          }
          this.$set(
            this.formModel,
            wItem.options.name,
            initialValue
          )
        } else if (wItem.type === 'select') {
          // if (
          //   wItem.options.optionSettingTabType === 'enter'
          // ) {
          //   this.$set(
          //     this.formModel,
          //     wItem.options.name,
          //     wItem.options.defaultValue
          //   )
          // } else if (
          //   wItem.options.optionSettingTabType === 'search'
          // ) {
          // this.$set(
          //   this.formModel,
          //   wItem.options.name,
          //   wItem.options.searchDefaultValue
          // )
          // }
          this.$set(
            this.formModel,
            wItem.options.name,
            wItem.options.defaultValue
          )
        } else {
          this.$set(
            this.formModel,
            wItem.options.name,
            deepClone(wItem.options.defaultValue)
          )
        }
      }
    },
    getWidgetName(widget) {
      return widget.type + '-widget'
    },

    disableFirefoxDefaultDrop() {
      let isFirefox =
        navigator.userAgent
          .toLowerCase()
          .indexOf('firefox') !== -1
      if (isFirefox) {
        document.body.ondrop = function (event) {
          event.stopPropagation()
          event.preventDefault()
        }
      }
    },

    onDragEnd(evt) {
      //console.log('drag end000', evt)
    },
    /**
     * 添加一个组件
     * @param evt VueDraggable内拖拽组件
     */
    onDragAdd(evt) {
      const selectedIndex = evt.newIndex
      const selectedWidget =
        this.designer.widgetList[selectedIndex]
      if (!!selectedWidget) {
        this.designer.setSelected(selectedWidget)
      }
      this.designer.emitHistoryChange()
      this.designer.emitEvent('field-selected', null)
    },
    /**
     * 组件调整位置
     */
    onDragUpdate() {
      /* 在VueDraggable内拖拽组件发生位置变化时会触发update，未发生组件位置变化不会触发！！ */
      this.designer.emitHistoryChange()
    },

    checkMove(evt) {
      return this.designer.checkWidgetMove(evt)
    },

    getFormData() {
      return this.formModel
    },

    getWidgetRef(widgetName, showError = false) {
      const foundRef = this.refList[widgetName]
      if (!foundRef && !!showError) {
        this.$message.error(
          this.i18nt('render.hint.refNotFound') + widgetName
        )
      }
      return foundRef
    },

    getSelectedWidgetRef() {
      let wName = this.designer.selectedWidgetName
      return this.getWidgetRef(wName)
    },

    clearWidgetRefList() {
      Object.keys(this.refList).forEach((key) => {
        delete this.refList[key]
      })
    },

    deleteWidgetRef(widgetRefName) {
      delete this.refList[widgetRefName]
    }
  }
}
</script>

<style lang="scss" scoped>
.form-widget-container {
  padding: 10px;
  background: #f1f2f3;

  overflow-x: hidden;
  overflow-y: auto;

  .el-form.full-height-width {
    height: 100%;
    padding: 3px;
    background: #ffffff;
    padding-bottom: 50px;
    .no-widget-hint {
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      font-size: 18px;
      color: #999999;
    }

    .form-widget-list {
      min-height: calc(100vh - 56px - 68px);
      padding: 3px;
    }
  }

  .el-form.Pad-layout {
    margin: 0 auto;
    max-width: 960px;
    border-radius: 15px;
    box-shadow: 0 0 1px 10px #495060;
  }

  .el-form.H5-layout {
    margin: 0 auto;
    width: 420px;
    border-radius: 15px;
    //border-width: 10px;
    box-shadow: 0 0 1px 10px #495060;
  }

  .el-form.widget-form ::v-deep .el-row {
    padding: 2px;
    border: 1px dashed rgba(170, 170, 170, 0.75);
  }
}

.grid-cell {
  min-height: 30px;
  border-right: 1px dotted #cccccc;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
}
</style>
