export default {
  computed: {
    isMobile() {
      const isMobile = /Mobi|Android|iPhone/i.test(
        navigator.userAgent
      ) //ua.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i)
      return isMobile
      // return false
    },
    isIos() {
      const isIos = /iPhone|iPad|iPod/i.test(
        navigator.userAgent
      )
      return isIos
    }
  }
}
