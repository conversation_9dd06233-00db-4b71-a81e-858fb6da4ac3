{"name": "variant-form", "version": "2.2.9", "private": false, "scripts": {"dev": "vue-cli-service serve", "serve": "vue-cli-service serve --open src/main.js", "build": "vue-cli-service build --report --dest dist/build", "lib": "vue-cli-service build --report --target lib --dest dist/lib --name VFormDesigner install.js", "postlib": "node scripts/replace-api.js", "lib-render": "vue-cli-service build --report --target lib --dest dist/lib-render --name VFormRender install-render.js", "lint": "vue-cli-service lint"}, "dependencies": {"clipboard": "^2.0.8", "core-js": "^3.6.5", "dayjs": "^1.11.13", "element-ui": "^2.15.1", "vue": "^2.6.11", "vue2-editor": "^2.10.2", "vuedraggable": "^2.24.3"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-service": "~4.5.0", "ace-builds": "^1.4.12", "babel-eslint": "^10.1.0", "babel-polyfill": "^6.26.0", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "mvdir": "^1.0.21", "sass": "^1.45.1", "sass-loader": "^8.0.2", "svg-sprite-loader": "^5.2.1", "vue-template-compiler": "^2.6.11", "husky": "^1.3.1", "prettier": "^2.8.7", "pretty-quick": "^1.8.0", "vcm-cli": "^1.0.6"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "volta": {"node": "14.21.3"}, "husky": {"hooks": {"pre-commit": "pretty-quick --staged", "commit-msg": "vcm"}}}